from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.user_do import SysUser
from module_customer.entity.do.customer_do import Customer, CustomerContact, CustomerInternalManager
from module_customer.entity.vo.customer_vo import CustomerPageQueryModel, CustomerQueryModel
from utils.page_util import PageUtil


class CustomerDao:
    """
    客户管理模块数据库操作层
    """

    @classmethod
    async def get_customer_list(cls, db: AsyncSession, query_object: CustomerQueryModel):
        """
        获取客户列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :return: 客户列表
        """
        query = select(Customer).where(Customer.del_flag == '0')

        # 根据查询条件过滤
        if query_object.customer_name:
            query = query.where(Customer.customer_name.like(f'%{query_object.customer_name}%'))
        if query_object.customer_level:
            query = query.where(Customer.customer_level == query_object.customer_level)
        if query_object.customer_type:
            query = query.where(Customer.customer_type == query_object.customer_type)
        if query_object.customer_source:
            query = query.where(Customer.customer_source == query_object.customer_source)
        if query_object.province:
            query = query.where(Customer.province == query_object.province)
        if query_object.city:
            query = query.where(Customer.city == query_object.city)
        if query_object.status:
            query = query.where(Customer.status == query_object.status)

        # 时间范围查询
        if query_object.begin_time and query_object.end_time:
            start_date = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            end_date = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_date, time(23, 59, 59))
            query = query.where(and_(Customer.create_time >= start_date, Customer.create_time <= end_time))

        # 排序
        query = query.order_by(desc(Customer.create_time))

        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_customer_page_list(cls, db: AsyncSession, query_object: CustomerPageQueryModel):
        """
        获取客户分页列表

        :param db: orm对象
        :param query_object: 分页查询参数对象
        :return: 客户分页列表
        """
        query = select(Customer).where(Customer.del_flag == '0')

        # 根据查询条件过滤
        if query_object.customer_name:
            query = query.where(Customer.customer_name.like(f'%{query_object.customer_name}%'))
        if query_object.customer_level:
            query = query.where(Customer.customer_level == query_object.customer_level)
        if query_object.customer_type:
            query = query.where(Customer.customer_type == query_object.customer_type)
        if query_object.customer_source:
            query = query.where(Customer.customer_source == query_object.customer_source)
        if query_object.province:
            query = query.where(Customer.province == query_object.province)
        if query_object.city:
            query = query.where(Customer.city == query_object.city)
        if query_object.status:
            query = query.where(Customer.status == query_object.status)

        # 时间范围查询
        if query_object.begin_time and query_object.end_time:
            start_date = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            end_date = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_date, time(23, 59, 59))
            query = query.where(and_(Customer.create_time >= start_date, Customer.create_time <= end_time))

        # 排序
        query = query.order_by(desc(Customer.create_time))

        # 分页
        return await PageUtil.paginate(db=db, query=query, page_num=query_object.page_num, page_size=query_object.page_size, is_page=True)

    @classmethod
    async def get_customer_by_id(cls, db: AsyncSession, customer_id: int):
        """
        根据客户ID获取客户详情

        :param db: orm对象
        :param customer_id: 客户ID
        :return: 客户详情
        """
        query = select(Customer).where(Customer.customer_id == customer_id, Customer.del_flag == '0')
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_customer_by_name(cls, db: AsyncSession, customer_name: str):
        """
        根据客户名称获取客户详情

        :param db: orm对象
        :param customer_name: 客户名称
        :return: 客户详情
        """
        query = select(Customer).where(Customer.customer_name == customer_name, Customer.del_flag == '0')
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_customer(cls, db: AsyncSession, customer: Customer):
        """
        新增客户

        :param db: orm对象
        :param customer: 客户对象
        :return: 新增的客户对象
        """
        db.add(customer)
        await db.flush()
        return customer

    @classmethod
    async def update_customer(cls, db: AsyncSession, customer: Customer):
        """
        更新客户

        :param db: orm对象
        :param customer: 客户对象
        :return: 更新结果
        """
        query = update(Customer).where(Customer.customer_id == customer.customer_id).values(
            customer_name=customer.customer_name,
            customer_level=customer.customer_level,
            parent_id=customer.parent_id,
            customer_type=customer.customer_type,
            customer_source=customer.customer_source,
            province=customer.province,
            city=customer.city,
            district=customer.district,
            address=customer.address,
            status=customer.status,
            update_by=customer.update_by,
            update_time=customer.update_time,
            remark=customer.remark
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_customer(cls, db: AsyncSession, customer_id: int, update_by: str):
        """
        删除客户（逻辑删除）

        :param db: orm对象
        :param customer_id: 客户ID
        :param update_by: 更新人
        :return: 删除结果
        """
        query = update(Customer).where(Customer.customer_id == customer_id).values(
            del_flag='2',
            update_by=update_by,
            update_time=datetime.now()
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def get_customer_contacts(cls, db: AsyncSession, customer_id: int):
        """
        获取客户联系人列表

        :param db: orm对象
        :param customer_id: 客户ID
        :return: 联系人列表
        """
        query = select(CustomerContact).where(
            CustomerContact.customer_id == customer_id,
            CustomerContact.status == '0'
        ).order_by(desc(CustomerContact.is_primary), desc(CustomerContact.create_time))
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def add_customer_contact(cls, db: AsyncSession, contact: CustomerContact):
        """
        新增客户联系人

        :param db: orm对象
        :param contact: 联系人对象
        :return: 新增的联系人对象
        """
        db.add(contact)
        await db.flush()
        return contact

    @classmethod
    async def update_customer_contact(cls, db: AsyncSession, contact: CustomerContact):
        """
        更新客户联系人

        :param db: orm对象
        :param contact: 联系人对象
        :return: 更新结果
        """
        query = update(CustomerContact).where(CustomerContact.contact_id == contact.contact_id).values(
            contact_name=contact.contact_name,
            position=contact.position,
            phone=contact.phone,
            wechat=contact.wechat,
            email=contact.email,
            is_primary=contact.is_primary,
            status=contact.status,
            update_by=contact.update_by,
            update_time=contact.update_time,
            remark=contact.remark
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_customer_contact(cls, db: AsyncSession, contact_id: int, update_by: str):
        """
        删除客户联系人

        :param db: orm对象
        :param contact_id: 联系人ID
        :param update_by: 更新人
        :return: 删除结果
        """
        query = update(CustomerContact).where(CustomerContact.contact_id == contact_id).values(
            status='1',
            update_by=update_by,
            update_time=datetime.now()
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def get_customer_internal_managers(cls, db: AsyncSession, customer_id: int):
        """
        获取客户内部负责人列表

        :param db: orm对象
        :param customer_id: 客户ID
        :return: 内部负责人列表
        """
        query = select(CustomerInternalManager, SysUser).where(
            CustomerInternalManager.customer_id == customer_id,
            CustomerInternalManager.user_id == SysUser.user_id
        ).order_by(desc(CustomerInternalManager.is_primary), desc(CustomerInternalManager.create_time))
        result = await db.execute(query)
        return result.all()

    @classmethod
    async def add_customer_internal_manager(cls, db: AsyncSession, manager: CustomerInternalManager):
        """
        新增客户内部负责人

        :param db: orm对象
        :param manager: 内部负责人对象
        :return: 新增的内部负责人对象
        """
        db.add(manager)
        await db.flush()
        return manager

    @classmethod
    async def update_customer_internal_manager(cls, db: AsyncSession, manager: CustomerInternalManager):
        """
        更新客户内部负责人

        :param db: orm对象
        :param manager: 内部负责人对象
        :return: 更新结果
        """
        query = update(CustomerInternalManager).where(CustomerInternalManager.id == manager.id).values(
            user_id=manager.user_id,
            is_primary=manager.is_primary,
            update_by=manager.update_by,
            update_time=manager.update_time
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_customer_internal_manager(cls, db: AsyncSession, manager_id: int):
        """
        删除客户内部负责人

        :param db: orm对象
        :param manager_id: 内部负责人关联ID
        :return: 删除结果
        """
        query = delete(CustomerInternalManager).where(CustomerInternalManager.id == manager_id)
        result = await db.execute(query)
        return result.rowcount
