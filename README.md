## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
3.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
4.  部门管理：配置系统组织机构（公司、部门、小组）。
5.  岗位管理：配置系统用户所属担任职务。
6.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7.  参数管理：对系统动态配置常用参数。
8.  通知公告：系统通知公告信息发布维护。
9.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除）任务调度包含执行结果日志。
13. 服务监控：监视当前系统CPU、内存、磁盘、堆栈等相关信息。
14. 缓存监控：对系统的缓存信息查询，命令统计等。
15. 在线构建器：拖动表单元素生成相应的HTML代码。
16. 系统接口：根据业务代码自动生成相关的api接口文档。
17. 代码生成：配置数据库表信息一键生成前后端代码（python、sql、vue、js），支持下载。

## 项目开发及发布相关

### 开发

```bash
# 进入项目根目录
cd lims
```

#### 前端
```bash
# 进入前端目录
cd front

# 安装依赖
npm install 或 yarn --registry=https://registry.npmmirror.com

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev 或 yarn dev
```

#### 后端
```bash
# 进入后端目录
cd back

# 安装依赖
pip install -e .

# 或者使用Poetry
poetry install

# 配置环境
在.env.dev文件中配置开发环境的数据库和redis

# 运行sql文件
1.新建数据库lims
2.如果使用的是MySQL数据库，使用命令或数据库连接工具运行sql文件夹下的init.sql；如果使用的是PostgreSQL数据库，使用命令或数据库连接工具运行sql文件夹下的init-pg.sql

# 数据库迁移
python scripts/generate_migration.py generate -m "initial migration"
python scripts/generate_migration.py apply

# 运行后端
python run.py
```

#### 访问
```bash
# 默认账号密码
账号：admin
密码：admin123

# 浏览器访问
地址：http://localhost:4000
```

### 发布

#### 前端
```bash
# 构建测试环境
npm run build:stage 或 yarn build:stage

# 构建生产环境
npm run build:prod 或 yarn build:prod
```

#### 后端
```bash
# 配置环境
在.env.prod文件中配置生产环境的数据库和redis

# 运行后端
python run.py --env=prod
```

### 测试

```bash
# 运行测试
pytest
```

### 查看swagger文档
http://localhost:4000/dev-api/docs

### 参考文档
那园的lims：
https://lims.labeden.com/
账号待补充

PMS系统 网址：http://***********:8080/PMS/homepage
账号：杜圣军
密码：dushengjun

需求文档：
https://docs.qq.com/doc/DZVpjZnJDVnB4Y0RH