import Layout from '@/layout'

export const basedataRoutes = {
    path: '/basedata',
    component: Layout,
    name: 'BaseData',
    meta: { title: '基础数据', icon: 'dict' },
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'technicalManual',
        component: () => import('@/views/basedata/technicalManual/index'),
        name: 'TechnicalManual',
        meta: { title: '技术手册', icon: 'list' }
      },
      {
        path: 'priceList',
        component: () => import('@/views/basedata/priceList/index'),
        name: 'PriceList',
        meta: { title: '价目表', icon: 'money' }
      },
      {
        path: 'technicalManualPrice',
        component: () => import('@/views/basedata/technicalManualPrice/index'),
        name: 'TechnicalManualPrice',
        meta: { title: '技术手册价格', icon: 'money' }
      }
    ]
}