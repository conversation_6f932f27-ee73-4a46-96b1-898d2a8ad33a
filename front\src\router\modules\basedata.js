import Layout from '@/layout'

export const basedataRoutes = {
    path: '/basedata',
    component: Layout,
    name: 'BaseData',
    meta: { title: '基础数据', icon: 'dict' },
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'technicalManual',
        component: () => import('@/views/basedata/technicalManual/index'),
        name: 'TechnicalManual',
        meta: { title: '技术手册', icon: 'list' }
      },
      {
        path: 'priceList',
        component: () => import('@/views/basedata/priceList/index'),
        name: 'PriceList',
        meta: { title: '价目表', icon: 'money' }
      },
      {
        path: 'technicalManualPrice',
        component: () => import('@/views/basedata/technicalManualPrice/index'),
        name: 'TechnicalManualPrice',
        meta: { title: '技术手册价格', icon: 'money' }
      },
      {
        path: 'categoryTreeSelectDemo',
        component: () => import('@/views/basedata/technicalManual/CategoryTreeSelectDemo'),
        name: 'CategoryTreeSelectDemo',
        meta: { title: '检测类别选择器演示', icon: 'tree' }
      },
      {
        path: 'testItemSelectorDemo',
        component: () => import('@/views/basedata/technicalManual/TestItemSelectorDemo'),
        name: 'TestItemSelectorDemo',
        meta: { title: '检测项目选择器演示', icon: 'list' }
      },
      {
        path: 'samplingPointArrangementDemo',
        component: () => import('@/views/basedata/technicalManual/SamplingPointArrangementDemo'),
        name: 'SamplingPointArrangementDemo',
        meta: { title: '采样点位安排演示', icon: 'location' }
      },
      {
        path: 'quotationDetailDemo',
        component: () => import('@/views/basedata/technicalManual/QuotationDetailDemo'),
        name: 'QuotationDetailDemo',
        meta: { title: '报价明细配置演示', icon: 'document' }
      }
    ]
}