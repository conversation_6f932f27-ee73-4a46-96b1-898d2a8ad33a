from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, Date
from config.database import Base


class TechnicalManual(Base):
    """
    技术手册表
    """

    __tablename__ = 'technical_manual'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    test_code = Column(String(20), nullable=True, comment='检测编号')
    category = Column(String(50), nullable=False, comment='检测类别')
    parameter = Column(String(50), nullable=False, comment='检测参数')
    method = Column(String(100), nullable=False, comment='检测方法')
    description = Column(Text, nullable=True, comment='技术描述')
    # 新增字段
    classification = Column(String(50), nullable=True, comment='分类')
    qualification_code = Column(String(50), nullable=True, unique=True, comment='资质编号')
    limitation_scope = Column(Text, nullable=True, comment='限制范围')
    common_alias = Column(String(200), nullable=True, comment='常用别名')
    qualification_date = Column(Date, nullable=True, comment='取得资质时间')
    # 原有字段
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    del_flag = Column(String(1), default='0', comment='删除标志（0存在 2删除）')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now(), comment='更新时间')
    remark = Column(String(500), default=None, comment='备注')
