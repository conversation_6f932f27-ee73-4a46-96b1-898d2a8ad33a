<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="检测类别" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入检测类别"
          clearable
          @blur="handleCategoryChange"
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="检测参数" prop="parameter">
        <el-input
          v-model="queryParams.parameter"
          placeholder="请输入检测参数"
          clearable
          @blur="handleParameterChange"
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="检测方法" prop="method">
        <el-input
          v-model="queryParams.method"
          placeholder="请输入检测方法"
          clearable
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="类别/参数/方法/标准号"
          clearable
          style="width: 200px; min-width: 100px;"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
           <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['basedata:technicalManual:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basedata:technicalManual:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basedata:technicalManual:remove']"
        >删除</el-button>
      </el-col>
      <!-- 删除批量录入按钮 -->
      <!-- <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Plus"
          @click="handleBatchInput"
          v-hasPermi="['basedata:technicalManual:add']"
        >批量录入</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Edit"
          :disabled="multiple"
          @click="handleBatchUpdate"
          v-hasPermi="['basedata:technicalManual:edit']"
        >批量修改</el-button>
      </el-col>
      <!-- 导出按钮 -->
      <el-col :span="1.5">
        <export-button export-url="/api/basedata/technical-manual/export" :query-params="queryParams" />
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="List"
          @click="handleCategorySelect"
        >选择检测类别</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Plus"
          @click="handleTestItemSelect"
        >选择检测项目</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Location"
          @click="handleSamplingPointArrangement"
        >安排采样点位</el-button>
      </el-col>
      <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="technicalManualList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="检测编号" align="center" prop="testCode" width="120" />
      <el-table-column label="检测类别" align="center" prop="category" />
      <el-table-column label="检测参数" align="center" prop="parameter" />
      <el-table-column label="检测方法" align="center" prop="method" show-overflow-tooltip />
      <el-table-column label="分类" align="center" prop="classification" />
      <el-table-column label="资质编号" align="center" prop="qualificationCode" width="120" />
      <el-table-column label="限制范围" align="center" prop="limitationScope" show-overflow-tooltip />
      <el-table-column label="常用别名" align="center" prop="commonAlias" show-overflow-tooltip />
      <el-table-column label="取得资质时间" align="center" prop="qualificationDate" width="120" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basedata:technicalManual:edit']"
          >修改</el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basedata:technicalManual:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改技术手册对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="technicalManualRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="检测编号" prop="testCode" v-if="form.testCode">
          <el-input v-model="form.testCode" disabled placeholder="系统自动生成" />
        </el-form-item>
        <el-form-item label="检测类别" prop="category">
          <el-input
            v-model="form.category"
            placeholder="请输入检测类别"
            @blur="handleFormCategoryChange"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测参数" prop="parameter">
          <el-input
            v-model="form.parameter"
            placeholder="请输入检测参数"
            @blur="handleFormParameterChange"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测方法" prop="method">
          <el-input
            v-model="form.method"
            placeholder="请输入检测方法"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="分类" prop="classification">
          <el-input v-model="form.classification" placeholder="请输入分类" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="资质编号" prop="qualificationCode">
          <el-input v-model="form.qualificationCode" placeholder="请输入资质编号" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="限制范围" prop="limitationScope">
          <el-input v-model="form.limitationScope" type="textarea" placeholder="请输入限制范围" :rows="3" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="常用别名" prop="commonAlias">
          <el-input v-model="form.commonAlias" placeholder="请输入常用别名" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="取得资质时间" prop="qualificationDate">
          <el-date-picker v-model="form.qualificationDate" type="date" placeholder="请选择取得资质时间" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="技术描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入技术描述" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" style="min-width: 100px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量录入技术手册对话框 -->
    <el-dialog title="批量录入技术手册" v-model="batchInputOpen" width="600px" append-to-body>
      <el-form ref="batchInputRef" :model="batchInputForm" :rules="batchInputRules" label-width="100px">
        <el-form-item label="检测类别" prop="categories">
          <el-input
            v-model="batchInputForm.categories"
            type="textarea"
            placeholder="请输入检测类别，多个用逗号分隔"
            :rows="3"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测参数" prop="parameters">
          <el-input
            v-model="batchInputForm.parameters"
            type="textarea"
            placeholder="请输入检测参数，多个用逗号分隔"
            :rows="3"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="检测方法" prop="methods">
          <el-input
            v-model="batchInputForm.methods"
            type="textarea"
            placeholder="请输入检测方法，多个用逗号分隔"
            :rows="3"
            style="min-width: 100px;"
          />
        </el-form-item>
        <el-form-item label="分类" prop="classification">
          <el-input v-model="batchInputForm.classification" placeholder="请输入分类" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="资质编号" prop="qualificationCode">
          <el-input v-model="batchInputForm.qualificationCode" placeholder="请输入资质编号" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="限制范围" prop="limitationScope">
          <el-input v-model="batchInputForm.limitationScope" type="textarea" placeholder="请输入限制范围" :rows="3" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="常用别名" prop="commonAlias">
          <el-input v-model="batchInputForm.commonAlias" placeholder="请输入常用别名" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="取得资质时间" prop="qualificationDate">
          <el-date-picker v-model="batchInputForm.qualificationDate" type="date" placeholder="请选择取得资质时间" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="batchInputForm.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="batchInputForm.remark" type="textarea" placeholder="请输入备注" style="min-width: 100px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchInputForm">确 定</el-button>
          <el-button @click="cancelBatchInput">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量更新技术手册对话框 -->
    <el-dialog title="批量更新技术手册" v-model="batchUpdateOpen" width="500px" append-to-body>
      <el-form ref="batchUpdateRef" :model="batchUpdateForm" label-width="100px">
        <el-form-item label="分类" prop="classification">
          <el-input v-model="batchUpdateForm.classification" placeholder="请输入分类" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="资质编号" prop="qualificationCode">
          <el-input v-model="batchUpdateForm.qualificationCode" placeholder="请输入资质编号" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="限制范围" prop="limitationScope">
          <el-input v-model="batchUpdateForm.limitationScope" type="textarea" placeholder="请输入限制范围" :rows="3" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="常用别名" prop="commonAlias">
          <el-input v-model="batchUpdateForm.commonAlias" placeholder="请输入常用别名" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="取得资质时间" prop="qualificationDate">
          <el-date-picker v-model="batchUpdateForm.qualificationDate" type="date" placeholder="请选择取得资质时间" style="min-width: 100px;" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="batchUpdateForm.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchUpdateForm">确 定</el-button>
          <el-button @click="cancelBatchUpdate">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 检测类别选择组件 -->
    <category-tree-select
      :visible="categorySelectOpen"
      @update:visible="categorySelectOpen = $event"
      :selected-values="selectedCategories"
      @confirm="handleCategoryConfirm"
      title="选择检测类别"
    />

    <!-- 检测项目选择组件 -->
    <test-item-selector
      :visible="testItemSelectOpen"
      @update:visible="testItemSelectOpen = $event"
      :category="testItemCategory"
      :selected-items="selectedTestItems"
      @confirm="handleTestItemConfirm"
      title="选择检测项目"
    />

    <!-- 采样点位安排组件 -->
    <sampling-point-arrangement
      :visible="samplingPointOpen"
      @update:visible="samplingPointOpen = $event"
      :initial-data="samplingPointData"
      @confirm="handleSamplingPointConfirm"
      title="安排采样点位"
    />
  </div>
</template>

<script setup name="TechnicalManual">
import {
  listTechnicalManual,
  pageTechnicalManual,
  getTechnicalManual,
  addTechnicalManual,
  updateTechnicalManual,
  delTechnicalManual,
  batchInputTechnicalManual,
  batchUpdateTechnicalManual,
  getCategoryOptions,
  getParameterOptions,
  getMethodOptions
} from "@/api/basedata/technicalManual";
import ExportButton from '@/components/ExportButton.vue';
import CategoryTreeSelect from '@/components/CategoryTreeSelect/index.vue';
import TestItemSelector from '@/components/TestItemSelector/index.vue';
import SamplingPointArrangement from '@/components/SamplingPointArrangement/index.vue';

const { proxy } = getCurrentInstance();
// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 技术手册表格数据
const technicalManualList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 是否显示批量录入弹出层
const batchInputOpen = ref(false);
// 是否显示批量更新弹出层
const batchUpdateOpen = ref(false);
// 是否显示检测类别选择弹出层
const categorySelectOpen = ref(false);
// 已选择的检测类别
const selectedCategories = ref([]);
// 是否显示检测项目选择弹出层
const testItemSelectOpen = ref(false);
// 检测项目选择的类别
const testItemCategory = ref('');
// 已选择的检测项目
const selectedTestItems = ref([]);
// 是否显示采样点位安排弹出层
const samplingPointOpen = ref(false);
// 采样点位安排数据
const samplingPointData = ref({});
// 采样点位安排结果
const samplingPointResult = ref({});
// 日期范围
const dateRange = ref([]);
// 检测类别选项
const categoryOptions = ref([]);
// 检测参数选项
const parameterOptions = ref([]);
// 检测方法选项
const methodOptions = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  category: undefined,
  parameter: undefined,
  method: undefined,
  keyword: undefined,
  beginTime: undefined,
  endTime: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  testCode: undefined,
  category: undefined,
  parameter: undefined,
  method: undefined,
  description: undefined,
  // 新增字段
  classification: undefined,
  qualificationCode: undefined,
  limitationScope: undefined,
  commonAlias: undefined,
  qualificationDate: undefined,
  // 原有字段
  status: "0",
  remark: undefined
});

// 表单校验
const rules = ref({
  category: [
    { required: true, message: "检测类别不能为空", trigger: "blur" }
  ],
  parameter: [
    { required: true, message: "检测参数不能为空", trigger: "blur" }
  ],
  method: [
    { required: true, message: "检测方法不能为空", trigger: "blur" }
  ]
});

// 批量录入表单参数
const batchInputForm = ref({
  categories: "",
  parameters: "",
  methods: "",
  // 新增字段
  classification: "",
  qualificationCode: "",
  limitationScope: "",
  commonAlias: "",
  qualificationDate: undefined,
  // 原有字段
  status: "0",
  remark: ""
});

// 批量录入表单校验
const batchInputRules = ref({
  categories: [
    { required: true, message: "检测类别不能为空", trigger: "blur" }
  ],
  parameters: [
    { required: true, message: "检测参数不能为空", trigger: "blur" }
  ],
  methods: [
    { required: true, message: "检测方法不能为空", trigger: "blur" }
  ]
});

// 批量更新表单参数
const batchUpdateForm = ref({
  ids: [],
  // 新增字段
  classification: "",
  qualificationCode: "",
  limitationScope: "",
  commonAlias: "",
  qualificationDate: undefined,
  // 原有字段
  status: "",
  remark: ""
});

/** 查询技术手册列表 */
function getList() {
  loading.value = true;
  // 处理日期范围
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.beginTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  } else {
    queryParams.value.beginTime = undefined;
    queryParams.value.endTime = undefined;
  }
  console.log("23jk")
  pageTechnicalManual(queryParams.value).then(response => {
    technicalManualList.value = response.data.rows;
    total.value = response.data.total;
    loading.value = false;
  });
}

/** 获取检测类别选项 */
function getCategoryOptionsList() {
  getCategoryOptions().then(response => {
    categoryOptions.value = response.data || [];
  });
}

/** 获取检测参数选项 */
function getParameterOptionsList(category) {
  getParameterOptions(category).then(response => {
    parameterOptions.value = response.data || [];
  });
}

/** 获取检测方法选项 */
function getMethodOptionsList(category, parameter) {
  getMethodOptions(category, parameter).then(response => {
    methodOptions.value = response.data || [];
  });
}

/** 检测类别变更 */
function handleCategoryChange() {
  // 输入框不需要清空参数和方法
  // 也不需要获取参数选项列表
}

/** 检测参数变更 */
function handleParameterChange() {
  // 输入框不需要清空方法
  // 也不需要获取方法选项列表
}

/** 表单检测类别变更 */
function handleFormCategoryChange() {
  // 输入框不需要清空参数和方法
  // 也不需要获取参数选项列表
}

/** 表单检测参数变更 */
function handleFormParameterChange() {
  // 输入框不需要清空方法
  // 也不需要获取方法选项列表
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    testCode: undefined,
    category: undefined,
    parameter: undefined,
    method: undefined,
    description: undefined,
    // 新增字段
    classification: undefined,
    qualificationCode: undefined,
    limitationScope: undefined,
    commonAlias: undefined,
    qualificationDate: undefined,
    // 原有字段
    status: "0",
    remark: undefined
  };
  proxy.resetForm("technicalManualRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加技术手册";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getTechnicalManual(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改技术手册";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["technicalManualRef"].validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateTechnicalManual(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTechnicalManual(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const manualIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除技术手册编号为"' + manualIds + '"的数据项?').then(function() {
    return delTechnicalManual(manualIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('basedata/technical-manual/export', {
    ...queryParams.value
  }, `技术手册_${new Date().getTime()}.xlsx`);
}

/** 批量录入按钮操作 */
function handleBatchInput() {
  resetBatchInputForm();
  batchInputOpen.value = true;
}

/** 批量录入表单重置 */
function resetBatchInputForm() {
  batchInputForm.value = {
    categories: "",
    parameters: "",
    methods: "",
    // 新增字段
    classification: "",
    qualificationCode: "",
    limitationScope: "",
    commonAlias: "",
    qualificationDate: undefined,
    // 原有字段
    status: "0",
    remark: ""
  };
  proxy.resetForm("batchInputRef");
}

/** 取消批量录入操作 */
function cancelBatchInput() {
  batchInputOpen.value = false;
  resetBatchInputForm();
}

/** 提交批量录入表单 */
function submitBatchInputForm() {
  proxy.$refs["batchInputRef"].validate(valid => {
    if (valid) {
      batchInputTechnicalManual(batchInputForm.value).then(response => {
        proxy.$modal.msgSuccess(response.data.message || "批量录入成功");
        batchInputOpen.value = false;
        getList();
      });
    }
  });
}

/** 批量更新按钮操作 */
function handleBatchUpdate() {
  const ids = selection.value.map(item => item.id);
  if (ids.length === 0) {
    proxy.$modal.msgError("请至少选择一条记录");
    return;
  }
  resetBatchUpdateForm();
  batchUpdateForm.value.ids = ids;
  batchUpdateOpen.value = true;
}

/** 批量更新表单重置 */
function resetBatchUpdateForm() {
  batchUpdateForm.value = {
    ids: [],
    pointCount: null,
    cycleType: "",
    cycleCount: null,
    frequency: null,
    sampleCount: null,
    serviceType: ""
  };
}

/** 取消批量更新操作 */
function cancelBatchUpdate() {
  batchUpdateOpen.value = false;
  resetBatchUpdateForm();
}

/** 提交批量更新表单 */
function submitBatchUpdateForm() {
  // 检查是否有要更新的字段
  const hasUpdateField =
    batchUpdateForm.value.pointCount !== null ||
    batchUpdateForm.value.cycleType !== "" ||
    batchUpdateForm.value.cycleCount !== null ||
    batchUpdateForm.value.frequency !== null ||
    batchUpdateForm.value.sampleCount !== null ||
    batchUpdateForm.value.serviceType !== "";

  if (!hasUpdateField) {
    proxy.$modal.msgError("请至少填写一个要更新的字段");
    return;
  }

  batchUpdateTechnicalManual(batchUpdateForm.value).then(response => {
    proxy.$modal.msgSuccess(response.data.message || "批量更新成功");
    batchUpdateOpen.value = false;
    getList();
  });
}

/** 选择检测类别按钮操作 */
function handleCategorySelect() {
  categorySelectOpen.value = true;
}

/** 处理检测类别选择确认 */
function handleCategoryConfirm(categories) {
  selectedCategories.value = [...categories];

  // 显示选择结果
  if (categories.length > 0) {
    proxy.$modal.msgSuccess(`已选择 ${categories.length} 个检测类别：${categories.join(', ')}`);
    console.log('选中的检测类别:', categories);

    // 这里可以根据选中的检测类别进行进一步的操作
    // 比如筛选技术手册列表、批量操作等
  } else {
    proxy.$modal.msgInfo('未选择任何检测类别');
  }
}

/** 选择检测项目按钮操作 */
function handleTestItemSelect() {
  // 检查是否已选择检测类别
  if (selectedCategories.value.length === 0) {
    proxy.$modal.msgWarning('请先选择检测类别');
    return;
  }

  // 使用第一个选中的检测类别
  testItemCategory.value = selectedCategories.value[0];
  testItemSelectOpen.value = true;
}

/** 处理检测项目选择确认 */
function handleTestItemConfirm(testItems) {
  selectedTestItems.value = [...testItems];

  // 显示选择结果
  if (testItems.length > 0) {
    proxy.$modal.msgSuccess(`已选择 ${testItems.length} 个检测项目`);
    console.log('选中的检测项目:', testItems);

    // 这里可以根据选中的检测项目进行进一步的操作
    // 比如生成报价单、创建检测任务等
  } else {
    proxy.$modal.msgInfo('未选择任何检测项目');
  }
}

/** 安排采样点位按钮操作 */
function handleSamplingPointArrangement() {
  // 可以设置一些初始数据
  samplingPointData.value = {
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1
  };

  samplingPointOpen.value = true;
}

/** 处理采样点位安排确认 */
function handleSamplingPointConfirm(arrangementData) {
  samplingPointResult.value = { ...arrangementData };

  // 显示安排结果
  proxy.$modal.msgSuccess(`采样点位安排完成：${arrangementData.pointName}`);
  console.log('采样点位安排结果:', arrangementData);

  // 这里可以根据安排结果进行进一步的操作
  // 比如保存到数据库、生成采样计划等
}

onMounted(() => {
  getList();
  getCategoryOptionsList();
});
</script>
