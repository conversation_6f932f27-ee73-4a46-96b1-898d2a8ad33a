<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="检测编号" prop="testCode">
        <el-input
          v-model="queryParams.testCode"
          placeholder="请输入检测编号"
          clearable
          style="min-width: 100px;"
        />
      </el-form-item>
      <el-form-item label="检测类别" prop="category">
        <el-select
          v-model="queryParams.category"
          placeholder="请选择检测类别"
          clearable
          @change="handleCategoryChange"
          style="min-width: 100px;"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.category"
            :label="item.category"
            :value="item.category"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检测参数" prop="parameter">
        <el-select
          v-model="queryParams.parameter"
          placeholder="请选择检测参数"
          clearable
          @change="handleParameterChange"
          style="min-width: 100px;"
        >
          <el-option
            v-for="item in parameterOptions"
            :key="item.parameter"
            :label="item.parameter"
            :value="item.parameter"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="检测方法" prop="method">
        <el-select
          v-model="queryParams.method"
          placeholder="请选择检测方法"
          clearable
          style="min-width: 100px;"
        >
          <el-option
            v-for="item in methodOptions"
            :key="item.method"
            :label="item.method"
            :value="item.method"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关键词" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="参数/方法"
          clearable
          style="width: 200px; min-width: 100px;"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="dateRange">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['basedata:priceList:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basedata:priceList:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basedata:priceList:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['basedata:priceList:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="priceListList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="报价编号" align="center" prop="priceCode" width="120" />
      <el-table-column label="检测编号" align="center" prop="testCode" width="120" />
      <el-table-column label="检测类别" align="center" prop="category" />
      <el-table-column label="检测参数" align="center" prop="parameter" />
      <el-table-column label="检测方法" align="center" prop="method" show-overflow-tooltip />
      <el-table-column label="采样单价" align="center" prop="samplingPrice">
        <template #default="scope">
          <span>{{ scope.row.samplingPrice ? scope.row.samplingPrice + ' 元' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检测单价" align="center" prop="testingPrice">
        <template #default="scope">
          <span>{{ scope.row.testingPrice + ' 元' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="差旅费单价" align="center" prop="travelPrice">
        <template #default="scope">
          <span>{{ scope.row.travelPrice ? scope.row.travelPrice + ' 元' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生效日期" align="center" prop="effectiveDate" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.effectiveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['basedata:priceList:edit']"
          >修改</el-button>
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['basedata:priceList:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改价目表对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="priceListRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="报价编号" prop="priceCode" v-if="form.priceCode">
          <el-input v-model="form.priceCode" disabled placeholder="系统自动生成" />
        </el-form-item>
        <el-form-item label="检测类别" prop="category">
          <el-select
            v-model="form.category"
            placeholder="请选择检测类别"
            style="width: 100%; min-width: 100px;"
            filterable
            @change="handleFormCategoryChange"
          >
            <el-option
              v-for="item in categoryOptions"
              :key="item.category"
              :label="item.category"
              :value="item.category"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检测参数" prop="parameter">
          <el-select
            v-model="form.parameter"
            placeholder="请选择检测参数"
            style="width: 100%; min-width: 100px;"
            filterable
            @change="handleFormParameterChange"
          >
            <el-option
              v-for="item in parameterOptions"
              :key="item.parameter"
              :label="item.parameter"
              :value="item.parameter"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检测方法" prop="method">
          <el-select
            v-model="form.method"
            placeholder="请选择检测方法"
            style="width: 100%; min-width: 100px;"
            filterable
            @change="handleFormMethodChange"
          >
            <el-option
              v-for="item in methodOptions"
              :key="item.method"
              :label="item.method"
              :value="item.method"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="检测编号" prop="testCode">
          <el-input v-model="form.testCode" disabled placeholder="根据检测类别、参数和方法自动带出" />
        </el-form-item>
        <el-form-item label="采样单价" prop="samplingPrice">
          <el-input-number
            v-model="form.samplingPrice"
            :precision="2"
            :step="0.01"
            :min="0"
            placeholder="请输入采样单价"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="检测单价" prop="testingPrice">
          <el-input-number
            v-model="form.testingPrice"
            :precision="2"
            :step="0.01"
            :min="0"
            placeholder="请输入检测单价"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="差旅费单价" prop="travelPrice">
          <el-input-number
            v-model="form.travelPrice"
            :precision="2"
            :step="0.01"
            :min="0"
            placeholder="请输入差旅费单价"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="生效日期" prop="effectiveDate">
          <el-date-picker
            v-model="form.effectiveDate"
            type="date"
            placeholder="请选择生效日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" style="min-width: 100px;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PriceList">
import {
  listPriceList,
  pagePriceList,
  getPriceList,
  addPriceList,
  updatePriceList,
  delPriceList,
} from "@/api/basedata/priceList";

import {
  getCategoryOptions,
  getParameterOptions,
  getMethodOptions,
  getTestCodeOptions,
  getTechnicalManualByTestCode,
  getTechnicalManualByParams
} from "@/api/basedata/technicalManual";

const { proxy } = getCurrentInstance();
// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 价目表表格数据
const priceListList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 日期范围
const dateRange = ref([]);
// 检测编号选项
const testCodeOptions = ref([]);
// 检测类别选项
const categoryOptions = ref([]);
// 检测参数选项
const parameterOptions = ref([]);
// 检测方法选项
const methodOptions = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  testCode: undefined,
  category: undefined,
  parameter: undefined,
  method: undefined,
  keyword: undefined,
  beginTime: undefined,
  endTime: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  priceCode: undefined,
  testCode: undefined,
  category: undefined,
  parameter: undefined,
  method: undefined,
  samplingPrice: undefined,
  testingPrice: undefined,
  travelPrice: undefined,
  effectiveDate: undefined,
  status: "0",
  remark: undefined
});

// 表单校验
const rules = ref({
  category: [
    { required: true, message: "检测类别不能为空", trigger: "blur" }
  ],
  parameter: [
    { required: true, message: "检测参数不能为空", trigger: "blur" }
  ],
  method: [
    { required: true, message: "检测方法不能为空", trigger: "blur" }
  ],
  testingPrice: [
    { required: true, message: "检测单价不能为空", trigger: "blur" }
  ],
  effectiveDate: [
    { required: true, message: "生效日期不能为空", trigger: "blur" }
  ]
});

/** 查询价目表列表 */
function getList() {
  loading.value = true;
  // 处理日期范围
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.beginTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  } else {
    queryParams.value.beginTime = undefined;
    queryParams.value.endTime = undefined;
  }

  pagePriceList(queryParams.value).then(response => {
    priceListList.value = response.data.rows;
    total.value = response.data.total;
    loading.value = false;
  });
}

/** 获取检测编号选项 */
function getTestCodeOptionsList() {
  getTestCodeOptions().then(response => {
    testCodeOptions.value = response.data || [];
  });
}

/** 获取检测类别选项 */
function getCategoryOptionsList() {
  getCategoryOptions().then(response => {
    categoryOptions.value = response.data || [];
  });
}

/** 获取检测参数选项 */
function getParameterOptionsList(category) {
  getParameterOptions(category).then(response => {
    parameterOptions.value = response.data || [];
  });
}

/** 获取检测方法选项 */
function getMethodOptionsList(category, parameter) {
  getMethodOptions(category, parameter).then(response => {
    methodOptions.value = response.data || [];
  });
}

/** 检测类别变更 */
function handleCategoryChange(val) {
  queryParams.value.parameter = undefined;
  queryParams.value.method = undefined;
  if (val) {
    getParameterOptionsList(val);
  } else {
    parameterOptions.value = [];
    methodOptions.value = [];
  }
}

/** 检测参数变更 */
function handleParameterChange(val) {
  queryParams.value.method = undefined;
  if (val && queryParams.value.category) {
    getMethodOptionsList(queryParams.value.category, val);
  } else {
    methodOptions.value = [];
  }
}

/** 表单检测类别变更 */
function handleFormCategoryChange(val) {
  form.value.parameter = undefined;
  form.value.method = undefined;
  if (val) {
    getParameterOptionsList(val);
  } else {
    parameterOptions.value = [];
    methodOptions.value = [];
  }
}

/** 表单检测参数变更 */
function handleFormParameterChange(val) {
  form.value.method = undefined;
  if (val && form.value.category) {
    getMethodOptionsList(form.value.category, val);
  } else {
    methodOptions.value = [];
  }
}

/** 检测编号变更 */
function handleTestCodeChange(val) {
  if (val) {
    getTechnicalManualByTestCode(val).then(response => {
      const technicalManual = response.data;
      form.value.category = technicalManual.category;
      form.value.parameter = technicalManual.parameter;
      form.value.method = technicalManual.method;
    });
  } else {
    form.value.category = undefined;
    form.value.parameter = undefined;
    form.value.method = undefined;
  }
}

/** 表单检测方法变更 */
function handleFormMethodChange(val) {
  if (val && form.value.category && form.value.parameter) {
    // 根据检测类别、参数和方法查询检测编号
    getTechnicalManualByParams(
      {category: form.value.category, 
       parameter: form.value.parameter, 
       method:val}).then(response => {
      const technicalManual = response.data;
      form.value.testCode = technicalManual.testCode;
    }).catch(error => {
      // 如果没有找到对应的技术手册，清空检测编号
      form.value.testCode = undefined;
      proxy.$modal.msgError(error.message || "未找到对应的技术手册");
    });
  } else {
    form.value.testCode = undefined;
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    priceCode: undefined,
    testCode: undefined,
    category: undefined,
    parameter: undefined,
    method: undefined,
    samplingPrice: undefined,
    testingPrice: undefined,
    travelPrice: undefined,
    effectiveDate: undefined,
    status: "0",
    remark: undefined
  };
  proxy.resetForm("priceListRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getCategoryOptionsList();
  open.value = true;
  title.value = "添加价目表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getCategoryOptionsList();
  const id = row.id || ids.value[0];
  getPriceList(id).then(response => {
    form.value = response.data;
    if (form.value.testCode) {
      getTechnicalManualByTestCode(form.value.testCode).then(response => {
        const technicalManual = response.data;
        form.value.category = technicalManual.category;
        // 获取检测参数选项
        getParameterOptionsList(technicalManual.category);
        form.value.parameter = technicalManual.parameter;
        // 获取检测方法选项
        getMethodOptionsList(technicalManual.category, technicalManual.parameter);
        form.value.method = technicalManual.method;
      });
    }
    open.value = true;
    title.value = "修改价目表";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["priceListRef"].validate(valid => {
    if (valid) {
      if (form.value.id) {
        updatePriceList(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addPriceList(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const priceListIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除价目表编号为"' + priceListIds + '"的数据项?').then(function() {
    return delPriceList(priceListIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('basedata/price-list/export', {
    ...queryParams.value
  }, `价目表_${new Date().getTime()}.xlsx`);
}

/** 初始化 */
function init() {
  getList();
  getCategoryOptionsList();
}

onMounted(() => {
  init();
});
</script>
