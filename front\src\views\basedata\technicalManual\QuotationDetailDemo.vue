<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>报价明细页面组件演示</span>
        </div>
      </template>

      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>功能说明</h3>
            <ul>
              <li>支持选择多个检测类别，每个类别对应一个Tab页面</li>
              <li>Tab风格为border-card，支持动态增加删除</li>
              <li>每个Tab下可以添加检测项目</li>
              <li>检测项目表格支持排序、筛选、多选、展开行</li>
              <li>支持为检测项目批量添加采样点位</li>
              <li>采样点位显示在展开行的嵌套表格中</li>
              <li>最终提交扁平化的数据结构</li>
            </ul>

            <div class="demo-controls">
              <el-button type="primary" @click="openQuotationDetail">
                打开报价明细配置
              </el-button>
              <el-button @click="clearResult">清空结果</el-button>
            </div>
          </el-col>

          <el-col :span="12">
            <h3>配置结果</h3>
            <div v-if="quotationResult.length > 0" class="result-display">
              <p><strong>扁平化数据（{{ quotationResult.length }}条记录）：</strong></p>
              <el-table
                :data="quotationResult"
                border
                style="width: 100%"
                max-height="400"
                size="small"
              >
                <el-table-column label="检测参数" prop="parameter" width="120" show-overflow-tooltip />
                <el-table-column label="检测方法" prop="method" width="150" show-overflow-tooltip />
                <el-table-column label="限制范围" prop="limitationScope" width="120" show-overflow-tooltip />
                <el-table-column label="项目备注" prop="projectRemark" width="120" show-overflow-tooltip />
                <el-table-column label="点位名称" prop="pointName" width="100" show-overflow-tooltip />
                <el-table-column label="点位数" prop="pointCount" width="80" />
                <el-table-column label="周期类型" prop="cycleType" width="80" />
                <el-table-column label="周期数" prop="cycleCount" width="80" />
                <el-table-column label="频次数" prop="frequency" width="80" />
                <el-table-column label="样品数" prop="sampleCount" width="80" />
                <el-table-column label="样品来源" prop="sampleSource" width="100" />
                <el-table-column label="是否分包" prop="isSubcontract" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.isSubcontract ? 'warning' : 'success'">
                      {{ row.isSubcontract ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>

              <div class="result-summary">
                <h4>数据统计：</h4>
                <el-descriptions :column="3" border>
                  <el-descriptions-item label="总记录数">{{ quotationResult.length }}</el-descriptions-item>
                  <el-descriptions-item label="检测参数数">{{ uniqueParameters.length }}</el-descriptions-item>
                  <el-descriptions-item label="采样点位数">{{ uniquePointNames.length }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            <div v-else class="no-result">
              <el-empty description="暂无配置结果" :image-size="100" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 报价明细组件 -->
    <quotation-detail
      :visible="quotationDetailOpen"
      @update:visible="quotationDetailOpen = $event"
      :initial-data="initialData"
      @confirm="handleQuotationDetailConfirm"
      title="报价明细配置"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import QuotationDetail from '@/components/QuotationDetail/index.vue'

// 是否显示报价明细弹出层
const quotationDetailOpen = ref(false)

// 初始数据
const initialData = ref({})

// 配置结果
const quotationResult = ref([])

// 计算属性：唯一的检测参数
const uniqueParameters = computed(() => {
  const parameters = quotationResult.value.map(item => item.parameter).filter(Boolean)
  return [...new Set(parameters)]
})

// 计算属性：唯一的采样点位名称
const uniquePointNames = computed(() => {
  const pointNames = quotationResult.value.map(item => item.pointName).filter(Boolean)
  return [...new Set(pointNames)]
})

/** 打开报价明细配置 */
function openQuotationDetail() {
  quotationDetailOpen.value = true
}

/** 处理报价明细配置确认 */
function handleQuotationDetailConfirm(result) {
  quotationResult.value = [...result]

  // 显示配置结果
  ElMessage.success(`报价明细配置完成，共 ${result.length} 条记录`)
  console.log('报价明细配置结果:', result)
}

/** 清空结果 */
function clearResult() {
  quotationResult.value = []
  ElMessage.info('已清空配置结果')
}
</script>

<style scoped>
.demo-content {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.result-display {
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
}

.result-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.result-summary h4 {
  margin: 0 0 15px 0;
  color: #d46b08;
}

.no-result {
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

h3 {
  color: #303133;
  margin-bottom: 15px;
}

ul {
  color: #606266;
  line-height: 1.6;
}

li {
  margin-bottom: 8px;
}
</style>
