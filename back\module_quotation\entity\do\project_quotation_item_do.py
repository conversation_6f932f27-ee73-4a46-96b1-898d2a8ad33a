"""
项目报价明细数据模型
"""
from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationItem(Base):
    """
    项目报价明细表
    """
    __tablename__ = 'project_quotation_item'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment='项目报价ID')
    item_code = Column(String(20), nullable=False, comment='项目编号')
    service_type = Column(String(50), nullable=False, comment='服务类型')
    
    # 检测信息
    category = Column(String(50), nullable=False, comment='检测类别')
    parameter = Column(String(50), nullable=False, comment='检测参数')
    method = Column(String(100), nullable=False, comment='检测方法')
    test_code = Column(String(20), nullable=True, comment='检测编号')
    price_code = Column(String(20), nullable=True, comment='报价编号')
    
    # 点位信息
    point_name = Column(String(100), nullable=True, comment='点位名称')
    point_count = Column(Integer, nullable=False, default=1, comment='点位数')
    
    # 周期信息
    cycle_type = Column(String(10), nullable=True, comment='周期类型')
    cycle_count = Column(Integer, nullable=True, default=1, comment='周期数')
    frequency = Column(Integer, nullable=True, default=1, comment='频次数')
    sample_count = Column(Integer, nullable=True, default=1, comment='样品数')
    
    # 价格信息
    sampling_price = Column(DECIMAL(10, 2), nullable=True, default=0, comment='采样单价')
    testing_price = Column(DECIMAL(10, 2), nullable=False, default=0, comment='检测单价')
    travel_price = Column(DECIMAL(10, 2), nullable=True, default=0, comment='差旅费单价')
    total_price = Column(DECIMAL(10, 2), nullable=False, default=0, comment='总价')
    
    # 备注
    remark = Column(Text, nullable=True, comment='备注')
    
    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment='创建人')
    create_time = Column(DateTime, nullable=True, comment='创建时间')
    update_by = Column(String(50), nullable=True, comment='更新人')
    update_time = Column(DateTime, nullable=True, comment='更新时间')
    
    # 关联项目报价
    project_quotation = relationship("ProjectQuotation", back_populates="items")
