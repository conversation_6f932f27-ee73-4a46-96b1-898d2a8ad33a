<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检测方法" prop="method">
        <el-input
          v-model="queryParams.method"
          placeholder="请输入检测方法"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检测类别" prop="categoryCode">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入检测类别"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="classification">
        <el-input
          v-model="queryParams.classification"
          placeholder="请输入分类"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="关键字" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入关键字"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['basedata:technicalManualPrice:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['basedata:technicalManualPrice:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['basedata:technicalManualPrice:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <ExportButton
          :query-params="queryParams"
          :date-range="dateRange"
          export-url="/basedata/technical-manual-price/export"
          file-name="技术手册价格"
          v-hasPermi="['basedata:technicalManualPrice:export']"
        />
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="technicalManualPriceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="检测类别" align="center" prop="category" />
      <el-table-column label="检测方法" align="center" prop="method" show-overflow-tooltip />
      <el-table-column label="采集单价" align="center" prop="samplingPrice" />
      <el-table-column label="检测首项单价" align="center" prop="firstItemPrice" width="120" />
      <el-table-column label="检测增项单价" align="center" prop="additionalItemPrice" width="120" />
      <el-table-column label="检测费上限" align="center" prop="testingFeeLimit" width="120" />
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basedata:technicalManualPrice:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basedata:technicalManualPrice:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改技术手册价格对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="technicalManualPriceRef" :model="form" :rules="rules" label-width="130px">
        <!-- 基本信息 -->
            <el-form-item label="检测方法" prop="method">
              <el-select
                v-model="form.method"
                filterable
                placeholder="请选择检测方法"
                @change="handleMethodChange"
                style="width: 90%;"
              >
                <el-option
                  v-for="option in methodOptions"
                  :key="option.method"
                  :label="option.method"
                  :value="option.method"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="检测类别" prop="categoryCode">
              <el-select
                v-model="form.categoryCode"
                filterable
                placeholder="请选择检测类别"
                @change="handleCategoryChange"
                style="width: 90%;"
              >
                <el-option
                  v-for="option in categoryOptions"
                  :key="option.categoryCode"
                  :label="option.displayText"
                  :value="option.category_code"
                />
              </el-select>
            </el-form-item>

        <!-- 检测价格相关字段 -->
        <el-divider content-position="left">检测价格</el-divider>
            <el-form-item label="检测首项单价" prop="firstItemPrice">
              <el-input-number v-model="form.firstItemPrice" :precision="2" :min="0" style="width: 40%;" />
            </el-form-item>
            <el-form-item label="检测增项单价" prop="additionalItemPrice">
              <el-input-number v-model="form.additionalItemPrice" :precision="2" :min="0" style="width: 40%;" />
            </el-form-item>
            <el-form-item label="检测费上限" prop="testingFeeLimit">
              <el-input-number v-model="form.testingFeeLimit" :precision="2" :min="0" style="width: 40%;" />
            </el-form-item>

        <!-- 采集价格相关字段 -->
        <el-divider content-position="left">采集价格</el-divider>
            <el-form-item label="采集单价" prop="samplingPrice">
              <el-input-number v-model="form.samplingPrice" :precision="2" :min="0" style="width: 40%;" />
            </el-form-item>
            <el-form-item label="前处理单价" prop="pretreatmentPrice">
              <el-input-number v-model="form.pretreatmentPrice" :precision="2" :min="0" style="width: 40%;" />
            </el-form-item>

        <!-- 其他价格相关字段 -->
        <!-- <el-divider content-position="left">其他价格</el-divider>
            <el-form-item label="分析特殊耗材单价" prop="specialConsumablesPrice">
              <el-input-number v-model="form.specialConsumablesPrice" :precision="2" :min="0" style="width: 40%;" />
            </el-form-item> -->

        <!-- 系统字段 -->
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="0">正常</el-radio>
                <el-radio label="1">停用</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
         
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TechnicalManualPrice">
import {
  listTechnicalManualPrice,
  pageTechnicalManualPrice,
  getTechnicalManualPrice,
  addTechnicalManualPrice,
  updateTechnicalManualPrice,
  delTechnicalManualPrice,
  getMethodCategoryOptions
} from "@/api/basedata/technicalManualPrice";
import ExportButton from '@/components/ExportButton.vue';

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 技术手册价格表格数据
const technicalManualPriceList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 日期范围
const dateRange = ref([]);
// 检测方法选项
const methodOptions = ref([]);
// 检测类别选项
const categoryOptions = ref([]);

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  method: undefined,
  category: undefined,
  classification: undefined,
  keyword: undefined,
  beginTime: undefined,
  endTime: undefined
});

// 表单参数
const form = ref({
  id: undefined,
  method: undefined,
  categoryCode: undefined,
  classification: undefined,
  // 检测价格相关字段
  firstItemPrice: undefined,
  additionalItemPrice: undefined,
  testingFeeLimit: undefined,
  // 采集价格相关字段
  samplingPrice: undefined,
  pretreatmentPrice: undefined,
  // 其他价格相关字段
  specialConsumablesPrice: undefined,
  // 系统字段
  status: "0",
  remark: undefined
});

// 表单校验
const rules = ref({
  method: [
    { required: true, message: "检测方法不能为空", trigger: "change" }
  ],
  categoryCode: [
    { required: true, message: "检测类别不能为空", trigger: "change" }
  ]
});

/** 查询技术手册价格列表 */
function getList() {
  loading.value = true;
  // 处理日期范围
  if (dateRange.value && dateRange.value.length > 0) {
    queryParams.value.beginTime = dateRange.value[0];
    queryParams.value.endTime = dateRange.value[1];
  } else {
    queryParams.value.beginTime = undefined;
    queryParams.value.endTime = undefined;
  }

  pageTechnicalManualPrice(queryParams.value).then(response => {
    technicalManualPriceList.value = response.data.rows;
    console.log("pageTechnicalManualPrice")
    total.value = response.data.total;
    loading.value = false;
  });
}

/** 获取检测方法和类别选项 */
function getMethodCategoryOptionsList() {
  getMethodCategoryOptions().then(response => {
    methodOptions.value = response.data || [];
  });
}

/** 检测方法变更 */
function handleMethodChange() {
  // 清空检测类别
  form.value.categoryCode = undefined;
  form.value.classification = undefined;

  // 获取对应的检测类别选项
  const selectedMethod = methodOptions.value.find(item => item.method === form.value.method);
  if (selectedMethod) {
    categoryOptions.value = selectedMethod.categories || [];
  } else {
    categoryOptions.value = [];
  }
}

/** 检测类别变更 */
function handleCategoryChange() {
  // 获取选中类别的分类信息
  const selectedCategory = categoryOptions.value.find(item => item.category === form.value.category);
  if (selectedCategory) {
    form.value.classification = selectedCategory.classification;
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: undefined,
    method: undefined,
    categoryCode: undefined,
    classification: undefined,
    // 检测价格相关字段
    firstItemPrice: undefined,
    additionalItemPrice: undefined,
    testingFeeLimit: undefined,
    // 采集价格相关字段
    samplingPrice: undefined,
    pretreatmentPrice: undefined,
    // 其他价格相关字段
    specialConsumablesPrice: undefined,
    // 系统字段
    status: "0",
    remark: undefined
  };
  categoryOptions.value = [];
  proxy.resetForm("technicalManualPriceRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加技术手册价格";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getTechnicalManualPrice(id).then(response => {
    form.value = response.data;

    // 设置检测方法对应的类别选项
    const selectedMethod = methodOptions.value.find(item => item.method === form.value.method);
    if (selectedMethod) {
      categoryOptions.value = selectedMethod.categories || [];
    }

    open.value = true;
    title.value = "修改技术手册价格";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["technicalManualPriceRef"].validate(valid => {
    if (valid) {
      if (form.value.id) {
        updateTechnicalManualPrice(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTechnicalManualPrice(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const priceIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除技术手册价格编号为"' + priceIds + '"的数据项?').then(function() {
    return delTechnicalManualPrice(priceIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

onMounted(() => {
  getList();
  getMethodCategoryOptionsList();
});
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
