# 项目报价费用计算功能实现文档

## 功能概述

本文档描述了项目报价费用计算功能的完整实现，包括基础价目表同步、多种费用计算逻辑以及相关的数据库设计和API接口。

## 核心功能

### 1. 基础价目表同步
- **表名**: `project_quotation_item_basedata_price`
- **功能**: 存储项目报价中每个类别-方法组合对应的价格信息
- **数据来源**: `technical_manual_price` 表
- **同步时机**: 每次新增或编辑项目报价时自动同步

### 2. 费用计算模块

#### 2.1 采样费用计算
```
采样费用 = 采样单价 × 点位数 × 监测周期 × 监测频率
```

#### 2.2 检测费用计算
```
检测单价计算逻辑：
- 如果类别-方法下有1个参数：检测单价 = 首项单价
- 如果类别-方法下有多个参数：检测单价 = 首项单价 + (n-1) × 增项单价
- 最大不能超过检测费上限

检测费用 = 检测单价 × 点位数 × 监测周期 × 监测频率 × 样品数
```

#### 2.3 前处理费用计算
```
前处理费用 = 前处理单价 × 点位数 × 监测周期 × 监测频率
```

#### 2.4 汇总费用
```
项目报价明细汇总费用 = 采样费用 + 检测费用 + 前处理费用
```

## 数据库设计

### 新增表：project_quotation_item_basedata_price

```sql
CREATE TABLE `project_quotation_item_basedata_price` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_quotation_id` int(11) NOT NULL COMMENT '项目报价ID',
  `project_quotation_item_id` varchar(20) NOT NULL COMMENT '项目明细的ID',
  
  -- 以下字段完全来源于 technical_manual_price 表
  `method` varchar(100) NOT NULL COMMENT '检测方法',
  `category` varchar(50) NOT NULL COMMENT '检测类别',
  `classification` varchar(50) DEFAULT NULL COMMENT '分类',
  `first_item_price` decimal(10,2) DEFAULT NULL COMMENT '检测首项单价',
  `additional_item_price` decimal(10,2) DEFAULT NULL COMMENT '检测增项单价',
  `testing_fee_limit` decimal(10,2) DEFAULT NULL COMMENT '检测费上限',
  `sampling_price` decimal(10,2) DEFAULT NULL COMMENT '采集单价',
  `pretreatment_price` decimal(10,2) DEFAULT NULL COMMENT '前处理单价',
  `special_consumables_price` decimal(10,2) DEFAULT NULL COMMENT '分析特殊耗材单价',
  
  -- 基础字段
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`id`),
  KEY `idx_project_quotation_id` (`project_quotation_id`),
  KEY `idx_category_method` (`category`, `method`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

## 核心类和方法

### 1. ProjectQuotationItemBasedataPrice (DO模型)
- 基础价目表数据模型
- 映射数据库表结构

### 2. ProjectQuotationItemBasedataPriceDao (DAO层)
- `get_unique_category_method_list()`: 获取不重复的类别-方法组合
- `delete_by_project_quotation_id()`: 删除项目相关的基础价目表数据
- `get_technical_manual_price_by_category_method()`: 从技术手册获取价格信息
- `batch_insert()`: 批量插入基础价目表数据
- `get_parameter_count_by_category_method()`: 获取参数数量

### 3. ProjectQuotationFeeCalculationService (服务层)
- `sync_basedata_prices()`: 同步基础价目表数据
- `calculate_project_fees()`: 计算项目费用
- `_calculate_group_fees()`: 计算同一类别-方法组的费用
- `_calculate_testing_unit_price()`: 计算检测单价

### 4. 控制器接口
- `GET /{id}/fee-calculation`: 计算项目报价费用
- `POST /{id}/sync-basedata-prices`: 同步基础价目表数据

## 业务流程

### 新增/编辑项目报价流程
1. 保存项目报价基本信息和明细
2. 提交数据库事务
3. 同步基础价目表数据：
   - 获取不重复的(category + method)列表
   - 删除现有基础价目表数据
   - 从技术手册价格表获取对应价格信息
   - 批量插入新的基础价目表数据
4. 计算项目费用：
   - 按类别-方法分组处理明细项
   - 计算每组的采样费用、检测费用、前处理费用
   - 汇总所有费用
5. 更新项目报价总费用

### 费用计算详细流程
1. 获取项目报价明细列表
2. 获取基础价目表数据
3. 按类别-方法分组明细项
4. 对每个组：
   - 统计参数数量
   - 计算检测单价（考虑首项、增项、上限）
   - 计算该组的各项费用
5. 汇总所有组的费用

## 测试验证

### 测试用例
运行 `test_fee_calculation.py` 验证：
- ✅ 单参数检测费用计算
- ✅ 多参数检测费用计算（首项+增项）
- ✅ 检测费用上限控制
- ✅ 采样费用计算
- ✅ 前处理费用计算
- ✅ 费用汇总计算

### 测试结果示例
```
总采样费用: 1160.00
总检测费用: 2640.00
总前处理费用: 600.00
项目总费用: 4400.00
```

## API接口

### 1. 计算项目报价费用
```
GET /quotation/project-quotation/{id}/fee-calculation
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "samplingFee": 1160.00,
    "testingFee": 2640.00,
    "pretreatmentFee": 600.00,
    "totalFee": 4400.00,
    "calculationDetails": {
      "details": [...],
      "summary": {...}
    }
  }
}
```

### 2. 同步基础价目表数据
```
POST /quotation/project-quotation/{id}/sync-basedata-prices
```

## 部署说明

### 1. 数据库迁移
执行迁移脚本：
```sql
-- 执行 create_project_quotation_item_basedata_price_table.sql
```

### 2. 依赖检查
确保以下表存在且有数据：
- `technical_manual_price`: 技术手册价格表
- `project_quotation`: 项目报价主表
- `project_quotation_item`: 项目报价明细表

### 3. 功能验证
1. 创建测试项目报价
2. 验证基础价目表数据同步
3. 验证费用计算结果
4. 验证API接口响应

## 注意事项

1. **数据一致性**: 基础价目表数据与技术手册价格表保持同步
2. **性能优化**: 大量明细项时考虑批量处理和缓存
3. **错误处理**: 处理技术手册价格表数据缺失的情况
4. **精度控制**: 使用Decimal类型确保金额计算精度
5. **事务管理**: 确保数据同步和费用计算的事务一致性

## 扩展性

1. **支持更多费用类型**: 可扩展特殊耗材费用等
2. **支持费用公式配置**: 可配置化费用计算公式
3. **支持批量费用调整**: 支持批量调整价格参数
4. **支持费用审批流程**: 集成费用审批机制
