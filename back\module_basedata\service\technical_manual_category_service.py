from typing import List, Dict, Any
from datetime import datetime

from module_basedata.dao.technical_manual_category_dao import TechnicalManualCategoryDao
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryModel,
    TechnicalManualCategoryQueryModel,
    TechnicalManualCategoryAddModel,
    TechnicalManualCategoryEditModel,
    TechnicalManualCategoryDeleteModel,
    TechnicalManualCategoryListModel,
)
from utils.page_util import PageResponseModel
from utils.common_util import CamelCaseUtil
from module_admin.entity.vo.user_vo import CurrentUserModel


class TechnicalManualCategoryService:
    """
    技术手册类目服务层
    """

    def __init__(self, dao: TechnicalManualCategoryDao):
        self.dao = dao

    def get_category_by_id(self, category_id: int) -> Dict[str, Any]:
        """
        根据ID获取技术手册类目详情
        """
        category = self.dao.get_by_id(category_id)
        if not category:
            raise ValueError("技术手册类目不存在")

        return CamelCaseUtil.transform_result(category)

    def get_category_by_code(self, category_code: str) -> Dict[str, Any]:
        """
        根据类目编号获取技术手册类目详情
        """
        category = self.dao.get_by_code(category_code)
        if not category:
            raise ValueError("技术手册类目不存在")

        return CamelCaseUtil.transform_result(category)

    def add_category(
        self, add_model: TechnicalManualCategoryAddModel, current_user: CurrentUserModel
    ) -> Dict[str, Any]:
        """
        新增技术手册类目
        """
        # 检查是否已存在相同的分类和检测类别
        existing = self.dao.get_by_classification_and_category(add_model.classification, add_model.category)
        if existing:
            raise ValueError(f"分类'{add_model.classification}'下的检测类别'{add_model.category}'已存在")

        # 创建类目数据
        category_data = TechnicalManualCategoryModel(
            classification=add_model.classification,
            category=add_model.category,
            remark=add_model.remark,
            create_by=current_user.user_name,
            create_time=datetime.now(),
            update_by=current_user.user_name,
            update_time=datetime.now(),
        )

        category = self.dao.create(category_data)
        return CamelCaseUtil.transform_result(category)

    def edit_category(
        self, edit_model: TechnicalManualCategoryEditModel, current_user: CurrentUserModel
    ) -> Dict[str, Any]:
        """
        编辑技术手册类目
        """
        category = self.dao.get_by_id(edit_model.id)
        if not category:
            raise ValueError("技术手册类目不存在")

        # 检查是否已存在相同的分类和检测类别（排除自己）
        existing = self.dao.get_by_classification_and_category(edit_model.classification, edit_model.category)
        if existing and existing.id != edit_model.id:
            raise ValueError(f"分类'{edit_model.classification}'下的检测类别'{edit_model.category}'已存在")

        # 更新数据
        category_data = TechnicalManualCategoryModel(
            classification=edit_model.classification,
            category=edit_model.category,
            status=edit_model.status,
            remark=edit_model.remark,
            update_by=current_user.user_name,
            update_time=datetime.now(),
        )

        updated_category = self.dao.update(category, category_data)
        return CamelCaseUtil.transform_result(updated_category)

    def delete_categories(self, delete_model: TechnicalManualCategoryDeleteModel) -> int:
        """
        批量删除技术手册类目
        """
        return self.dao.delete_by_ids(delete_model.ids)

    def get_category_list(self, query_model: TechnicalManualCategoryQueryModel) -> List[Dict[str, Any]]:
        """
        获取技术手册类目列表
        """
        categories = self.dao.get_list(query_model)
        return [CamelCaseUtil.transform_result(category) for category in categories]

    def get_category_page(self, query_model: TechnicalManualCategoryQueryModel) -> PageResponseModel:
        """
        分页查询技术手册类目
        """
        page_result = self.dao.get_page(query_model)

        # 转换结果
        rows = [CamelCaseUtil.transform_result(category) for category in page_result.rows]

        return PageResponseModel(
            total=page_result.total, rows=rows, page_num=page_result.page_num, page_size=page_result.page_size
        )

    def get_all_active_categories(self) -> List[Dict[str, Any]]:
        """
        获取所有有效的技术手册类目
        """
        categories = self.dao.get_all_active()
        return [CamelCaseUtil.transform_result(category) for category in categories]

    def get_classifications(self) -> List[str]:
        """
        获取所有分类
        """
        return self.dao.get_classifications()

    def get_categories_by_classification(self, classification: str) -> List[str]:
        """
        根据分类获取检测类别列表
        """
        return self.dao.get_categories_by_classification(classification)

    def get_category_options(self) -> Dict[str, List[str]]:
        """
        获取分类和检测类别的选项数据
        """
        classifications = self.get_classifications()
        result = {}

        for classification in classifications:
            categories = self.get_categories_by_classification(classification)
            result[classification] = categories

        return result

    def get_category_list_for_select(self) -> List[TechnicalManualCategoryListModel]:
        """
        获取用于下拉选择的类目列表
        """
        categories = self.dao.get_all_active()
        return [
            TechnicalManualCategoryListModel(
                category_code=category.category_code, classification=category.classification, category=category.category
            )
            for category in categories
        ]

    def create_categories_if_not_exists(
        self, classification: str, categories: List[str], current_user: CurrentUserModel
    ) -> List[str]:
        """
        批量创建类目（如果不存在），返回类目编号列表
        """
        # 去重并过滤空值
        unique_categories = list(set([cat.strip() for cat in categories if cat.strip()]))

        created_categories = self.dao.batch_create_if_not_exists(classification, unique_categories)

        return [category.category_code for category in created_categories]

    def get_category_code_by_classification_and_category(self, classification: str, category: str) -> str:
        """
        根据分类和检测类别获取类目编号
        """
        category_obj = self.dao.get_by_classification_and_category(classification, category)
        if not category_obj:
            raise ValueError(f"未找到分类'{classification}'下的检测类别'{category}'")

        return category_obj.category_code

    def get_classification_and_category_by_code(self, category_code: str) -> Dict[str, str]:
        """
        根据类目编号获取分类和检测类别
        """
        category = self.dao.get_by_code(category_code)
        if not category:
            raise ValueError(f"未找到类目编号'{category_code}'")

        return {"classification": category.classification, "category": category.category}
