<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body>
    <el-form :model="form" :rules="rules" ref="customerRef" label-width="100px">
      <el-tabs v-model="activeName">
        <el-tab-pane label="基本信息" name="basic">
          <el-row>
            <el-col :span="12">
              <el-form-item label="客户级别" prop="customerLevel">
                <el-radio-group v-model="form.customerLevel" @change="handleLevelChange">
                  <el-radio :value="'1'" label="集团">集团</el-radio>
                  <el-radio :value="'2'" label="公司">公司</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.customerLevel === '2'">
              <el-form-item label="所属集团" prop="parentId">
                <el-select v-model="form.parentId" placeholder="请选择所属集团" clearable>
                  <el-option
                    v-for="item in groupOptions"
                    :key="item.customerId"
                    :label="item.customerName"
                    :value="item.customerId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="form.customerName"
                  placeholder="请输入客户名称"
                  maxlength="100"
                  @blur="handleCheckCustomerNameUnique"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户类型" prop="customerType">
                <el-select v-model="form.customerType" placeholder="请选择客户类型">
                  <el-option label="政府" value="政府" />
                  <el-option label="企业" value="企业" />
                  <el-option label="园区" value="园区" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="客户来源" prop="customerSource">
                <el-select v-model="form.customerSource" placeholder="请选择客户来源">
                  <el-option label="自主开发" value="自主开发" />
                  <el-option label="合作伙伴" value="合作伙伴" />
                  <el-option label="客户推荐" value="客户推荐" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-radio-group v-model="form.status">
                  <el-radio :value="'0'" label="正常">正常</el-radio>
                  <el-radio :value="'1'" label="停用">停用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="地址" prop="province">
                <el-cascader
                  v-model="regionValue"
                  :options="regionOptions"
                  placeholder="请选择地区"
                  @change="handleRegionChange"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="详细地址" prop="address">
                <el-input
                  v-model="form.address"
                  placeholder="请输入详细地址"
                  maxlength="255"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入备注"
                  maxlength="500"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="联系人信息" name="contacts">
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="Plus" @click="handleAddContact">添加联系人</el-button>
            </el-col>
          </el-row>
          <el-table :data="form.contacts" style="margin-top: 10px;">
            <el-table-column label="联系人姓名" align="center" prop="contactName">
              <template #default="scope">
                <el-input v-model="scope.row.contactName" placeholder="请输入联系人姓名" />
              </template>
            </el-table-column>
            <el-table-column label="职务" align="center" prop="position">
              <template #default="scope">
                <el-input v-model="scope.row.position" placeholder="请输入职务" />
              </template>
            </el-table-column>
            <el-table-column label="电话" align="center" prop="phone">
              <template #default="scope">
                <el-input v-model="scope.row.phone" placeholder="请输入电话" />
              </template>
            </el-table-column>
            <el-table-column label="微信" align="center" prop="wechat">
              <template #default="scope">
                <el-input v-model="scope.row.wechat" placeholder="请输入微信" />
              </template>
            </el-table-column>
            <el-table-column label="邮箱" align="center" prop="email">
              <template #default="scope">
                <el-input v-model="scope.row.email" placeholder="请输入邮箱" />
              </template>
            </el-table-column>
            <el-table-column label="主要联系人" align="center" prop="isPrimary" width="100">
              <template #default="scope">
                <el-checkbox v-model="scope.row.isPrimary" true-value="1" false-value="0" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template #default="scope">
                <el-button link type="primary" icon="Delete" @click="handleDeleteContact(scope.$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="内部负责人" name="managers">
          <el-row>
            <el-col :span="24">
              <el-button type="primary" icon="Plus" @click="handleAddManager">添加负责人</el-button>
            </el-col>
          </el-row>
          <el-table :data="form.internalManagers" style="margin-top: 10px;">
            <el-table-column label="负责人" align="center" prop="userId">
              <template #default="scope">
                <el-select v-model="scope.row.userId" placeholder="请选择负责人">
                  <el-option
                    v-for="item in userOptions"
                    :key="item.userId"
                    :label="item.nickName"
                    :value="item.userId"
                  ></el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="主要负责人" align="center" prop="isPrimary" width="100">
              <template #default="scope">
                <el-checkbox v-model="scope.row.isPrimary" true-value="1" false-value="0" />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="100">
              <template #default="scope">
                <el-button link type="primary" icon="Delete" @click="handleDeleteManager(scope.$index)" />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';
import { listCustomer, checkCustomerNameUnique } from "@/api/customer";
import { listUser } from "@/api/system/user";
import { regionData } from 'element-china-area-data'

const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  open: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["cancel", "submit"]);
const { proxy } = getCurrentInstance();

const dialogVisible = computed({
  get: () => props.open,
  set: (val) => {
    if (!val) {
      emit("cancel");
    }
  }
});

const activeName = ref("basic");
const groupOptions = ref([]);
const userOptions = ref([]);
const regionOptions = ref(regionData);
const regionValue = ref([]);

const defaultForm = {
  customerId: undefined,
  customerName: undefined,
  customerLevel: "1",
  parentId: undefined,
  customerType: undefined,
  customerSource: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  address: undefined,
  status: "0",
  remark: undefined,
  contacts: [],
  internalManagers: []
};

const data = reactive({
  form: { ...defaultForm },
  rules: {
    customerName: [
      { required: true, message: "客户名称不能为空", trigger: "blur" },
      { max: 100, message: "客户名称长度不能超过100个字符", trigger: "blur" }
    ],
    customerLevel: [
      { required: true, message: "客户级别不能为空", trigger: "change" }
    ],
    parentId: [
      { required: true, message: "所属集团不能为空", trigger: "change" }
    ],
    customerType: [
      { required: true, message: "客户类型不能为空", trigger: "change" }
    ],
    customerSource: [
      { required: true, message: "客户来源不能为空", trigger: "change" }
    ],
    province: [
      { required: true, message: "省份不能为空", trigger: "change" }
    ],
    city: [
      { required: true, message: "城市不能为空", trigger: "change" }
    ]
  }
});

const { form, rules } = toRefs(data);

// 监听对话框打开状态
watch(() => props.open, (val) => {
  if (val) {
    // 获取集团列表
    getGroupList();
    // 获取用户列表
    getUserList();
  }
});

// 监听表单数据变化，更新地区选择器的值
watch(() => form.value, (val) => {
  if (val.province && val.city) {
    const provinceCode = regionOptions.value.find(item => item.label === val.province)?.value;
    const cityCode = provinceCode ?
      regionOptions.value.find(item => item.value === provinceCode)?.children.find(item => item.label === val.city)?.value :
      undefined;

    if (provinceCode && cityCode) {
      regionValue.value = val.district ?
        [provinceCode, cityCode, regionOptions.value.find(item => item.value === provinceCode)?.children.find(item => item.value === cityCode)?.children.find(item => item.label === val.district)?.value] :
        [provinceCode, cityCode];
    }
  }
}, { deep: true });

// 获取集团列表
function getGroupList() {
  console.log("开始获取集团列表");
  listCustomer({ customerLevel: '1' }).then(response => {
    console.log("获取集团列表响应:", response);

    // 检查响应数据结构
    if (response.data && Array.isArray(response.data)) {
      console.log("响应数据是数组，长度:", response.data.length);
      if (response.data.length > 0) {
        console.log("第一个集团数据:", response.data[0]);
        console.log("第一个集团的属性:", Object.keys(response.data[0]));

        // 检查属性名称是否为驼峰命名
        const firstGroup = response.data[0];
        console.log("检查属性名称:");
        console.log("- customerId 存在:", 'customerId' in firstGroup);
        console.log("- customerName 存在:", 'customerName' in firstGroup);
        console.log("- customer_id 存在:", 'customer_id' in firstGroup);
        console.log("- customer_name 存在:", 'customer_name' in firstGroup);
      }
    } else {
      console.log("响应数据不是数组或为空");
    }

    groupOptions.value = response.data || [];
    console.log("设置集团列表后:", groupOptions.value);
  }).catch(error => {
    console.error("获取集团列表失败:", error);
  });
}

// 获取用户列表
function getUserList() {
  listUser({ status: '0' }).then(response => {
    userOptions.value = response.rows || [];
  });
}

// 客户级别变更
function handleLevelChange(val) {
  if (val === '1') {
    form.value.parentId = undefined;
  }
}

// 地区选择变更
function handleRegionChange(val) {
  if (val && val.length > 0) {
    // 根据选择的地区代码获取地区名称
    const province = regionOptions.value.find(item => item.value === val[0]);
    form.value.province = province?.label;

    if (val.length > 1) {
      const city = province?.children.find(item => item.value === val[1]);
      form.value.city = city?.label;

      if (val.length > 2) {
        const district = city?.children.find(item => item.value === val[2]);
        form.value.district = district?.label;
      } else {
        form.value.district = undefined;
      }
    } else {
      form.value.city = undefined;
      form.value.district = undefined;
    }
  } else {
    form.value.province = undefined;
    form.value.city = undefined;
    form.value.district = undefined;
  }
}

// 校验客户名称是否唯一
function handleCheckCustomerNameUnique() {
  if (form.value.customerName) {
    checkCustomerNameUnique(form.value.customerName, form.value.customerId).then(response => {
      if (!response.data) {
        proxy.$modal.msgError("客户名称已存在");
        form.value.customerName = "";
      }
    });
  }
}

// 添加联系人
function handleAddContact() {
  form.value.contacts.push({
    contactId: undefined,
    customerId: form.value.customerId,
    contactName: undefined,
    position: undefined,
    phone: undefined,
    wechat: undefined,
    email: undefined,
    isPrimary: "0",
    status: "0"
  });
}

// 删除联系人
function handleDeleteContact(index) {
  form.value.contacts.splice(index, 1);
}

// 添加内部负责人
function handleAddManager() {
  form.value.internalManagers.push({
    id: undefined,
    customerId: form.value.customerId,
    userId: undefined,
    isPrimary: "0"
  });
}

// 删除内部负责人
function handleDeleteManager(index) {
  form.value.internalManagers.splice(index, 1);
}

// 表单重置
function reset() {
  console.log("重置表单前的数据:", JSON.stringify(form.value));
  form.value = { ...defaultForm };
  console.log("重置表单后的数据:", JSON.stringify(form.value));
  regionValue.value = [];
  activeName.value = "basic";
  proxy.resetForm("customerRef");
}

// 设置表单数据
function setFormData(data) {
  console.log("设置表单数据:", JSON.stringify(data));
  // 深拷贝数据，避免引用问题
  const newData = JSON.parse(JSON.stringify(data));
  // 重置表单
  reset();
  // 设置表单数据
  Object.keys(newData).forEach(key => {
    form.value[key] = newData[key];
  });
  console.log("设置后的表单数据:", JSON.stringify(form.value));
}

// 取消按钮
function cancel() {
  reset();
  emit("cancel");
}

// 提交表单
function submitForm() {
  proxy.$refs["customerRef"].validate(valid => {
    if (valid) {
      // 验证联系人
      if (form.value.contacts.length === 0) {
        proxy.$modal.msgError("请至少添加一个联系人");
        activeName.value = "contacts";
        return;
      }

      // 验证内部负责人
      if (form.value.internalManagers.length === 0) {
        proxy.$modal.msgError("请至少添加一个内部负责人");
        activeName.value = "managers";
        return;
      }

      // 验证联系人必填项
      for (let i = 0; i < form.value.contacts.length; i++) {
        const contact = form.value.contacts[i];
        if (!contact.contactName) {
          proxy.$modal.msgError(`第${i+1}个联系人的姓名不能为空`);
          activeName.value = "contacts";
          return;
        }
      }

      // 验证内部负责人必填项
      for (let i = 0; i < form.value.internalManagers.length; i++) {
        const manager = form.value.internalManagers[i];
        if (!manager.userId) {
          proxy.$modal.msgError(`第${i+1}个内部负责人不能为空`);
          activeName.value = "managers";
          return;
        }
      }

      // 触发submit事件，将表单数据传递给父组件
      emit("submit", form.value);
    }
  });
}

// 对外暴露方法
defineExpose({
  reset,
  form,
  setFormData
});
</script>
