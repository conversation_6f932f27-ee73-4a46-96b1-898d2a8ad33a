<template>
  <div class="quotation-detail">
    <el-dialog
      :title="title"
      v-model="dialogVisible"
      width="95%"
      append-to-body
      @close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="detail-content">
        <!-- 顶部操作区 -->
        <div class="top-actions">
          <el-button type="primary" @click="openCategorySelector">
            <el-icon><Plus /></el-icon>
            新增检测类别
          </el-button>
        </div>

        <!-- Tab页面 -->
        <el-tabs
          v-model="currentCategory"
          type="border-card"
          closable
          @tab-remove="removeTab"
          style="margin-top: 20px;"
        >
          <el-tab-pane
            v-for="category in categories"
            :key="category"
            :label="category"
            :name="category"
          >
            <div class="tab-content">
              <!-- 检测项目操作区 -->
              <div class="test-item-actions">
                <el-button type="primary" @click="openTestItemSelector(category)">
                  <el-icon><Plus /></el-icon>
                  新增检测项目
                </el-button>
                <el-button
                  type="danger"
                  :disabled="!selectedTestItems[category] || selectedTestItems[category].length === 0"
                  @click="batchDeleteTestItems(category)"
                >
                  <el-icon><Delete /></el-icon>
                  删除检测项目 ({{ selectedTestItems[category]?.length || 0 }})
                </el-button>
                <el-tooltip content="在检测项目下安排新增采样点位" placement="top">
                  <el-button
                    type="warning"
                    :disabled="!selectedTestItems[category] || selectedTestItems[category].length === 0"
                    @click="batchAddSamplingPoints(category)"
                  >
                    <el-icon><Location /></el-icon>
                    新增采样点位 ({{ selectedTestItems[category]?.length || 0 }})
                  </el-button>
                </el-tooltip>
              </div>

              <!-- 检测项目表格 -->
              <el-table
                :data="testItemsData[category] || []"
                @selection-change="(selection) => handleTestItemSelection(category, selection)"
                style="width: 100%; margin-top: 15px;"
                row-key="id"
                :expand-row-keys="expandedRows[category] || []"
                @expand-change="handleExpandChange"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column type="expand" width="50">
                  <template #default="{ row }">
                    <div class="sampling-points-section">
                      <div class="section-header">
                        <h4>采样点位信息</h4>
                        <div class="section-actions">
                          <el-button
                            type="danger"
                            size="small"
                            :disabled="!selectedSamplingPoints[`${category}_${row.id}`] || selectedSamplingPoints[`${category}_${row.id}`].length === 0"
                            @click="batchDeleteSamplingPoints(category, row.id)"
                          >
                            删除 ({{ selectedSamplingPoints[`${category}_${row.id}`]?.length || 0 }})
                          </el-button>
                          <el-button
                            type="danger"
                            size="small"
                            @click="clearSamplingPoints(category, row.id)"
                          >
                            清空
                          </el-button>
                        </div>
                      </div>

                      <!-- 采样点位表格 -->
                      <el-table
                        :data="samplingPointsData[`${category}_${row.id}`] || []"
                        style="width: 100%;"
                        size="small"
                        @selection-change="(selection) => handleSamplingPointSelection(category, row.id, selection)"
                      >
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column label="点位名称" prop="pointName" min-width="150">
                          <template #default="{ row }">
                            <el-tooltip :content="row.pointName" placement="top">
                              <span>{{ row.pointName }}</span>
                            </el-tooltip>
                          </template>
                        </el-table-column>
                        <el-table-column label="点位数" prop="pointCount" min-width="80">
                          <template #default="{ row }">
                            <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                              <span>{{ row.pointCount }}</span>
                            </el-tooltip>
                          </template>
                        </el-table-column>
                        <el-table-column label="检测周期类型" prop="cycleType" min-width="100" />
                        <el-table-column label="检测周期数" prop="cycleCount" min-width="100" />
                        <el-table-column label="检测频次数" prop="frequency" min-width="100">
                          <template #default="{ row }">
                            <el-tooltip content="检测频次(次/检测周期)" placement="top">
                              <span>{{ row.frequency }}</span>
                            </el-tooltip>
                          </template>
                        </el-table-column>
                        <el-table-column label="样品数" prop="sampleCount" min-width="80">
                          <template #default="{ row }">
                            <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                              <span>{{ row.sampleCount }}</span>
                            </el-tooltip>
                          </template>
                        </el-table-column>
                        <el-table-column label="样品来源" prop="sampleSource" min-width="100">
                          <template #default="{ row }">
                            <el-select
                              v-model="row.sampleSource"
                              placeholder="选择样品来源"
                              size="small"
                              style="width: 100%;"
                            >
                              <el-option label="采样" value="采样" />
                              <el-option label="检测" value="检测" />
                            </el-select>
                          </template>
                        </el-table-column>
                        <el-table-column label="是否分包" prop="isSubcontract" width="150">
                          <template #default="{ row }">
                            <el-switch
                              v-model="row.isSubcontract"
                               inline-prompt
                              active-text="是"
                              inactive-text="否"
                              active-value="1"
                              inactive-value="0"
                              size="small"
                            />
                          </template>
                        </el-table-column>
                        <!-- <el-table-column label="操作" width="80" fixed="right">
                          <template #default="{ row, $index }">
                            <el-button
                              type="danger"
                              size="small"
                              @click="deleteSamplingPoint(category, row.testItemId, $index)"
                            >
                              删除
                            </el-button>
                          </template>
                        </el-table-column> -->
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="检测参数"
                  prop="parameter"
                  min-width="150"
                  show-overflow-tooltip
                  sortable
                  :filters="getParameterFilters(category)"
                  :filter-method="filterParameter"
                />
                <el-table-column
                  label="检测方法"
                  prop="method"
                  min-width="200"
                  show-overflow-tooltip
                  sortable
                  :filters="getMethodFilters(category)"
                  :filter-method="filterMethod"
                >
                  <template #default="{ row }">
                    <el-select
                      v-model="row.method"
                      placeholder="选择检测方法"
                      style="width: 100%;"
                      @change="handleMethodChange(row)"
                      filterable
                      no-data-text="无可用检测方法"
                    >
                      <el-option
                        v-for="method in getAvailableMethods(category, row.parameter)"
                        :key="method"
                        :label="method"
                        :value="method"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column
                  label="限制范围"
                  prop="limitationScope"
                  min-width="200"
                  show-overflow-tooltip
                  sortable
                />
                <el-table-column label="项目备注" min-width="150">
                  <template #default="{ row }">
                    <el-input
                      v-model="row.remark"
                      placeholder="请输入项目备注"
                      size="small"
                    />
                  </template>
                </el-table-column>
                <!-- <el-table-column label="操作" width="100" fixed="right">
                  <template #default="{ $index }">
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteTestItem(category, $index)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column> -->
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 空状态 -->
        <div v-if="categories.length === 0" class="empty-state">
          <el-empty description="请先选择检测类别开始配置报价明细" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="success" @click="handleSubmit" :disabled="!hasData">
            <el-icon><Check /></el-icon>
            提 交
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 检测类别选择组件 -->
    <category-tree-select
      :visible="categorySelectOpen"
      @update:visible="categorySelectOpen = $event"
      :selected-values="categories"
      @confirm="handleCategoryConfirm"
      title="选择检测类别"
    />

    <!-- 检测项目选择组件 -->
    <test-item-selector
      :visible="testItemSelectOpen"
      @update:visible="testItemSelectOpen = $event"
      :category="currentCategory"
      :selected-items="getCurrentSelectedItems()"
      @confirm="handleTestItemConfirm"
      title="选择检测项目"
    />

    <!-- 采样点位安排组件 -->
    <sampling-point-arrangement
      :visible="samplingPointOpen"
      @update:visible="samplingPointOpen = $event"
      :initial-data="samplingPointInitialData"
      @confirm="handleSamplingPointConfirm"
      title="安排采样点位"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Check, Delete, Location } from '@element-plus/icons-vue'
import CategoryTreeSelect from '@/components/CategoryTreeSelect/index.vue'
import TestItemSelector from '@/components/TestItemSelector/index.vue'
import SamplingPointArrangement from '@/components/SamplingPointArrangement/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '报价明细配置'
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前激活的Tab
const currentCategory = ref('')
// 检测类别列表
const categories = ref([])
// 每个类别下的检测项目数据
const testItemsData = ref({})
// 每个类别下选中的检测项目
const selectedTestItems = ref({})
// 每个检测项目下选中的采样点位
const selectedSamplingPoints = ref({})
// 采样点位数据
const samplingPointsData = ref({})
// 展开的行
const expandedRows = ref({})

// 组件状态
const categorySelectOpen = ref(false)
const testItemSelectOpen = ref(false)
const samplingPointOpen = ref(false)
// const currentCategory = ref('')
const currentSelectedTestItems = ref([])
const samplingPointInitialData = ref({})

// 可用的检测方法缓存
const availableMethods = ref({})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 计算是否有数据
const hasData = computed(() => {
  return categories.value.some(category => {
    const items = testItemsData.value[category]
    return items && items.length > 0
  })
})

// 初始化数据
function initData() {
  if (props.initialData && Object.keys(props.initialData).length > 0) {
    // 从初始数据恢复状态
    categories.value = props.initialData.categories || []
    testItemsData.value = props.initialData.testItemsData || {}
    samplingPointsData.value = props.initialData.samplingPointsData || {}

    if (categories.value.length > 0) {
      currentCategory.value = categories.value[0]
    }
  }
  // 注意：这里不再重置数据，保持现有数据状态
  // 只有在明确需要重置时才调用resetData()
}

// 重置数据
function resetData() {
  categories.value = []
  testItemsData.value = {}
  selectedTestItems.value = {}
  samplingPointsData.value = {}
  selectedSamplingPoints.value = {}
  expandedRows.value = {}
  currentCategory.value = ''
}

// 打开检测类别选择器
function openCategorySelector() {
  categorySelectOpen.value = true
}

// 处理检测类别选择确认
function handleCategoryConfirm(selectedCategories) {
  const newCategories = selectedCategories.filter(cat => !categories.value.includes(cat))

  // 添加新的类别
  newCategories.forEach(category => {
    categories.value.push(category)
    testItemsData.value[category] = []
    selectedTestItems.value[category] = []
    expandedRows.value[category] = []
  })

  // 设置激活的Tab
  if (newCategories.length > 0) {
    currentCategory.value = newCategories[0]
  }

  if (newCategories.length > 0) {
    ElMessage.success(`已添加 ${newCategories.length} 个检测类别`)
  }
}

// 移除Tab
function removeTab(targetName) {
  ElMessageBox.confirm(
    `确定要删除检测类别"${targetName}"及其所有数据吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const index = categories.value.indexOf(targetName)
    if (index > -1) {
      categories.value.splice(index, 1)
      delete testItemsData.value[targetName]
      delete selectedTestItems.value[targetName]
      delete expandedRows.value[targetName]

      // 清理相关的采样点位数据
      Object.keys(samplingPointsData.value).forEach(key => {
        if (key.startsWith(`${targetName}_`)) {
          delete samplingPointsData.value[key]
        }
      })

      // 设置新的激活Tab
      if (currentCategory.value === targetName) {
        currentCategory.value = categories.value.length > 0 ? categories.value[0] : ''
      }

      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 打开检测项目选择器
function openTestItemSelector(category) {
  currentCategory.value = category
  testItemSelectOpen.value = true
}

// 获取当前选中的检测项目
function getCurrentSelectedItems() {
  return testItemsData.value[currentCategory.value] || []
}

// 处理检测项目选择确认
function handleTestItemConfirm(selectedItems) {
  if (!testItemsData.value[currentCategory.value]) {
    testItemsData.value[currentCategory.value] = []
  }

  // 添加新的检测项目，避免重复
  const existingIds = testItemsData.value[currentCategory.value].map(item => item.id)
  const newItems = selectedItems.filter(item => !existingIds.includes(item.id))

  testItemsData.value[currentCategory.value].push(...newItems)

  if (newItems.length > 0) {
    ElMessage.success(`已添加 ${newItems.length} 个检测项目`)
  }
}

// 处理检测项目选择变化
function handleTestItemSelection(category, selection) {
  selectedTestItems.value[category] = selection
}

// 批量删除检测项目
function batchDeleteTestItems(category) {
  const selectedItems = selectedTestItems.value[category] || []
  if (selectedItems.length === 0) {
    ElMessage.warning('请先选择要删除的检测项目')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedItems.length} 个检测项目吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const selectedIds = selectedItems.map(item => item.id)
    testItemsData.value[category] = testItemsData.value[category].filter(
      item => !selectedIds.includes(item.id)
    )

    // 清理相关的采样点位数据
    selectedIds.forEach(id => {
      delete samplingPointsData.value[`${category}_${id}`]
    })

    selectedTestItems.value[category] = []
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

// 删除单个检测项目
function deleteTestItem(category, index) {
  const item = testItemsData.value[category][index]

  ElMessageBox.confirm(
    `确定要删除检测项目"${item.parameter}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    testItemsData.value[category].splice(index, 1)

    // 清理相关的采样点位数据
    delete samplingPointsData.value[`${category}_${item.id}`]

    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

// 批量新增采样点位
function batchAddSamplingPoints(category) {
  const selectedItems = selectedTestItems.value[category] || []
  if (selectedItems.length === 0) {
    ElMessage.warning('请先选择要添加采样点位的检测项目')
    return
  }

  currentSelectedTestItems.value = selectedItems
  samplingPointInitialData.value = {
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1
  }
  samplingPointOpen.value = true
}

// 处理采样点位安排确认
function handleSamplingPointConfirm(arrangementData) {
  currentSelectedTestItems.value.forEach(testItem => {
    const key = `${currentCategory.value}_${testItem.id}`
    if (!samplingPointsData.value[key]) {
      samplingPointsData.value[key] = []
    }

    // 添加采样点位数据
    samplingPointsData.value[key].push({
      ...arrangementData,
      testItemId: testItem.id,
    })
    console.log("samplingPointsData:", samplingPointsData.value)
    // 展开对应的行
    if (!expandedRows.value[currentCategory.value]) {
      expandedRows.value[currentCategory.value] = []
    }
    expandedRows.value[currentCategory.value].push(testItem.id)
    
  })

  ElMessage.success(`已为 ${currentSelectedTestItems.value.length} 个检测项目添加采样点位`)
}

// 处理展开行变化
function handleExpandChange(row, expandedRows) {
  // 这里可以添加展开行变化的逻辑
}

// 清空采样点位
function clearSamplingPoints(category, testItemId) {
  ElMessageBox.confirm(
    '确定要清空该检测项目的所有采样点位吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    const key = `${category}_${testItemId}`
    delete samplingPointsData.value[key]
    ElMessage.success('清空成功')
  }).catch(() => {
    // 取消清空
  })
}

// 处理采样点位选择变化
function handleSamplingPointSelection(category, testItemId, selection) {
  const key = `${category}_${testItemId}`
  selectedSamplingPoints.value[key] = selection
}

// 批量删除采样点位
function batchDeleteSamplingPoints(category, testItemId) {
  const key = `${category}_${testItemId}`
  const selectedItems = selectedSamplingPoints.value[key] || []

  if (selectedItems.length === 0) {
    ElMessage.warning('请先选择要删除的采样点位')
    return
  }

  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedItems.length} 个采样点位吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 获取选中项的索引
    const samplingPoints = samplingPointsData.value[key] || []
    const selectedIndexes = selectedItems.map(item => samplingPoints.indexOf(item)).filter(index => index !== -1)

    // 从后往前删除，避免索引变化
    selectedIndexes.sort((a, b) => b - a).forEach(index => {
      samplingPoints.splice(index, 1)
    })

    // 清空选择
    selectedSamplingPoints.value[key] = []

    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除
  })
}

// 删除单个采样点位
function deleteSamplingPoint(category, testItemId, index) {
  const key = `${category}_${testItemId}`
  if (samplingPointsData.value[key]) {
    samplingPointsData.value[key].splice(index, 1)
    ElMessage.success('删除成功')
  }
}

// 获取参数过滤器选项
function getParameterFilters(category) {
  const items = testItemsData.value[category] || []
  const parameters = [...new Set(items.map(item => item.parameter))]
  return parameters.map(param => ({ text: param, value: param }))
}

// 参数过滤方法
function filterParameter(value, row) {
  return row.parameter === value
}

// 获取检测方法过滤器选项
function getMethodFilters(category) {
  const items = testItemsData.value[category] || []
  const methods = [...new Set(items.map(item => item.method).filter(Boolean))]
  return methods.map(method => ({ text: method, value: method }))
}

// 检测方法过滤方法
function filterMethod(value, row) {
  return row.method === value
}

// 获取可用的检测方法
function getAvailableMethods(category, parameter) {
  // 这里应该根据检测类别和检测参数从技术手册接口获取可用的检测方法
  // 暂时返回一个示例数组，实际应该调用API
  if (!category || !parameter) {
    return []
  }

  // TODO: 调用技术手册API获取检测方法
  // const response = await getMethodOptions(category, parameter)
  // return response.data || []

  return ['方法1', '方法2', '方法3']
}

// 处理检测方法变化
function handleMethodChange(row) {
  // 这里可以添加检测方法变化的逻辑
  console.log('检测方法变化:', row)
}

// 处理提交
function handleSubmit() {
  const result = generateFlattenedData()
  if (result.length === 0) {
    ElMessage.warning('请先配置检测项目和采样点位')
    return
  }

  // 获取当前组件状态
  const currentState = {
    categories: categories.value,
    testItemsData: testItemsData.value,
    samplingPointsData: samplingPointsData.value
  }
  console.log("配置明细项目", result, currentState)
  emit('confirm', result, currentState)
  ElMessage.success('提交成功')
  handleClose()
}

// 生成扁平化数据
function generateFlattenedData() {
  const result = []

  categories.value.forEach(category => {
    const testItems = testItemsData.value[category] || []

    testItems.forEach(testItem => {
      const samplingPoints = samplingPointsData.value[`${category}_${testItem.id}`] || []

      if (samplingPoints.length > 0) {
        samplingPoints.forEach(samplingPoint => {
          result.push({
            // 检测项目信息
            category: category,
            classification: testItem.classification,
            parameter: testItem.parameter,
            method: testItem.method,
            limitationScope: testItem.limitationScope,
            remark: testItem.remark || '',
            // 采样点位信息
            pointName: samplingPoint.pointName,
            pointCount: samplingPoint.pointCount,
            cycleType: samplingPoint.cycleType,
            cycleCount: samplingPoint.cycleCount,
            frequency: samplingPoint.frequency,
            sampleCount: samplingPoint.sampleCount,
            sampleSource: samplingPoint.sampleSource,
            isSubcontract: samplingPoint.isSubcontract
          })
        })
      } else {
        // 如果没有采样点位，只添加检测项目信息
        result.push({
          category: category,
          classification: testItem.classification,
          parameter: testItem.parameter,
          method: testItem.method,
          limitationScope: testItem.limitationScope,
          remark: testItem.remark || '',
          pointName: '',
          pointCount: 0,
          cycleType: '',
          cycleCount: 0,
          frequency: 0,
          sampleCount: 0,
          sampleSource: '',
          isSubcontract: "0",
        })
      }
    })
  })

  return result
}

// 处理关闭
function handleClose() {
  dialogVisible.value = false
  emit('update:visible', false)
}
</script>

<style scoped>
.quotation-detail {
  width: 100%;
}

.detail-content {
  padding: 10px 0;
  min-height: 500px;
}

.top-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.tab-content {
  padding: 20px 0;
}

.test-item-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.sampling-points-section {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 10px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 表格样式 */
:deep(.el-table) {
  border: 1px solid #ebeef5;
}

:deep(.el-table th) {
  background-color: #fafafa;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>