<template>
  <div class="app-container">
    <a-card :bordered="false">
      <!-- 查询条件 -->
      <a-form layout="inline" :model="queryParams" @finish="handleQuery">
        <a-form-item label="检测参数" name="parameter">
          <a-select
            v-model:value="queryParams.parameter"
            placeholder="请选择检测参数"
            allowClear
            style="min-width: 100px;"
            @change="handleParameterChange"
          >
            <a-select-option v-for="item in parameterOptions" :key="item.parameter" :value="item.parameter">
              {{ item.parameter }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检测方法" name="method">
          <a-select
            v-model:value="queryParams.method"
            placeholder="请选择检测方法"
            allowClear
            style="min-width: 100px;"
          >
            <a-select-option v-for="item in methodOptions" :key="item.method" :value="item.method">
              {{ item.method }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="关键字" name="keyword">
          <a-input v-model:value="queryParams.keyword" placeholder="请输入关键字" allowClear />
        </a-form-item>
        <a-form-item label="创建时间" name="dateRange">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
            @change="handleDateRangeChange"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button @click="resetQuery">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 操作按钮 -->
      <div style="margin-bottom: 16px; margin-top: 16px;">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增
          </a-button>
          <!-- 导出按钮 -->
          <export-button export-url="/api/basedata/price-list/export" :query-params="queryParams" />
        </a-space>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="loading"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: pagination.showSizeChanger,
          showTotal: pagination.showTotal,
          onChange: (page, pageSize) => handleTableChange({current: page, pageSize: pageSize})
        }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === '0' ? 'green' : 'red'">
              {{ record.status === '0' ? '启用' : '停用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除该价目表吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 新增/编辑弹窗 -->
      <a-modal
        :visible="open"
        :title="title"
        :confirm-loading="submitLoading"
        @ok="handleSubmit"
        @cancel="cancel"
      >
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="检测参数" name="parameter">
            <a-select
              v-model:value="form.parameter"
              placeholder="请选择检测参数"
              style="width: 100%"
              @change="handleFormParameterChange"
            >
              <a-select-option v-for="item in parameterOptions" :key="item.parameter" :value="item.parameter">
                {{ item.parameter }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="检测方法" name="method">
            <a-select
              v-model:value="form.method"
              placeholder="请选择检测方法"
              style="width: 100%"
            >
              <a-select-option v-for="item in methodOptions" :key="item.method" :value="item.method">
                {{ item.method }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="采样费用" name="samplingPrice">
            <a-input-number v-model:value="form.samplingPrice" placeholder="请输入采样费用" style="width: 100%" />
          </a-form-item>
          <a-form-item label="检测费用" name="testingPrice">
            <a-input-number v-model:value="form.testingPrice" placeholder="请输入检测费用" style="width: 100%" />
          </a-form-item>
          <a-form-item label="差旅费用" name="travelPrice">
            <a-input-number v-model:value="form.travelPrice" placeholder="请输入差旅费用" style="width: 100%" />
          </a-form-item>
          <a-form-item label="生效日期" name="effectiveDate">
            <a-date-picker v-model:value="form.effectiveDate" format="YYYY-MM-DD" style="width: 100%" />
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-radio-group v-model:value="form.status">
              <a-radio value="0">启用</a-radio>
              <a-radio value="1">停用</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import {
  getPriceListPage,
  getPriceListDetail,
  addPriceList,
  editPriceList,
  deletePriceList,
  getParameters,
  getMethods
} from '@/api/basedata/price-list';
import ExportButton from '@/components/ExportButton.vue';
import dayjs from 'dayjs';

export default defineComponent({
  name: 'PriceList',
  components: {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    ExportButton
  },
  setup() {
    // 列表数据
    const list = ref([]);
    const loading = ref(false);
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`
    });

    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      parameter: undefined,
      method: undefined,
      keyword: undefined,
      beginTime: undefined,
      endTime: undefined
    });
    const dateRange = ref([]);

    // 下拉选项
    const parameterOptions = ref([]);
    const methodOptions = ref([]);

    // 表单数据
    const formRef = ref(null);
    const open = ref(false);
    const title = ref('');
    const submitLoading = ref(false);
    const form = reactive({
      id: undefined,
      parameter: undefined,
      method: undefined,
      samplingPrice: 0,
      testingPrice: 0,
      travelPrice: 0,
      effectiveDate: undefined,
      status: '0',
      remark: undefined
    });
    const rules = {
      parameter: [{ required: true, message: '请选择检测参数', trigger: 'change' }],
      method: [{ required: true, message: '请选择检测方法', trigger: 'change' }],
      samplingPrice: [{ required: true, message: '请输入采样费用', trigger: 'blur' }],
      testingPrice: [{ required: true, message: '请输入检测费用', trigger: 'blur' }],
      effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
      status: [{ required: true, message: '请选择状态', trigger: 'change' }]
    };

    // 表格列定义
    const columns = [
      { title: '检测参数', dataIndex: 'parameter', key: 'parameter', width: 150 },
      { title: '检测方法', dataIndex: 'method', key: 'method', width: 150 },
      { title: '采样费用', dataIndex: 'samplingPrice', key: 'samplingPrice', width: 120 },
      { title: '检测费用', dataIndex: 'testingPrice', key: 'testingPrice', width: 120 },
      { title: '差旅费用', dataIndex: 'travelPrice', key: 'travelPrice', width: 120 },
      { title: '生效日期', dataIndex: 'effectiveDate', key: 'effectiveDate', width: 120 },
      { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
      { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 180 },
      { title: '操作', key: 'action', width: 150 }
    ];

    // 获取列表数据
    const getList = async () => {
      loading.value = true;
      try {
        const res = await getPriceListPage(queryParams);
        // 检查返回数据格式
        if (res.data && Array.isArray(res.data.rows)) {
          // 标准格式：{rows: [...], total: 100}
          list.value = res.data.rows;
          pagination.total = res.data.total;
        } else if (res.data && Array.isArray(res.data.records)) {
          // 另一种可能的格式：{records: [...], total: 100}
          list.value = res.data.records;
          pagination.total = res.data.total;
        } else if (Array.isArray(res.data)) {
          // 直接返回数组的情况
          list.value = res.data;
          pagination.total = res.data.length;
        } else {
          list.value = [];
          pagination.total = 0;
          console.error('未知的返回数据格式', res.data);
        }

        // 更新分页信息
        pagination.current = queryParams.pageNum;
        pagination.pageSize = queryParams.pageSize;
      } catch (error) {
        console.error('获取价目表列表失败', error);
        message.error('获取价目表列表失败');
        list.value = [];
        pagination.total = 0;
      } finally {
        loading.value = false;
      }
    };

    // 获取检测参数选项
    const getParameterOptions = async () => {
      try {
        const res = await getParameters();
        parameterOptions.value = res.data || [];
      } catch (error) {
        console.error('获取检测参数失败', error);
        message.error('获取检测参数失败');
      }
    };

    // 获取检测方法选项
    const getMethodOptions = async (parameter) => {
      try {
        const res = await getMethods(parameter);
        methodOptions.value = res.data || [];
      } catch (error) {
        console.error('获取检测方法失败', error);
        message.error('获取检测方法失败');
      }
    };

    // 查询条件变更处理
    const handleParameterChange = (value) => {
      queryParams.parameter = value;
      queryParams.method = undefined;
      getMethodOptions(value);
    };

    const handleDateRangeChange = (_, dateStrings) => {
      queryParams.beginTime = dateStrings[0] || undefined;
      queryParams.endTime = dateStrings[1] || undefined;
    };

    // 表单选择变更处理
    const handleFormParameterChange = (value) => {
      form.parameter = value;
      form.method = undefined;
      getMethodOptions(value);
    };

    // 查询操作
    const handleQuery = () => {
      queryParams.pageNum = 1;
      getList();
    };

    // 重置查询
    const resetQuery = () => {
      dateRange.value = [];
      Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        parameter: undefined,
        method: undefined,
        keyword: undefined,
        beginTime: undefined,
        endTime: undefined
      });
      getList();
    };

    // 表格变更处理
    const handleTableChange = (pag) => {
      // 更新分页参数
      queryParams.pageNum = pag.current;
      queryParams.pageSize = pag.pageSize;
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      // 重新获取数据
      getList();
    };

    // 新增操作
    const handleAdd = () => {
      open.value = true;
      title.value = '新增价目表';
      resetForm();
    };

    // 编辑操作
    const handleEdit = async (record) => {
      open.value = true;
      title.value = '编辑价目表';
      resetForm();

      try {
        const res = await getPriceListDetail(record.id);
        const data = res.data;

        // 处理日期格式
        if (data.effectiveDate) {
          data.effectiveDate = dayjs(data.effectiveDate);
        }

        Object.assign(form, data);

        // 加载相关选项
        await getMethodOptions(form.parameter);
      } catch (error) {
        console.error('获取价目表详情失败', error);
        message.error('获取价目表详情失败');
      }
    };

    // 删除操作
    const handleDelete = async (record) => {
      try {
        await deletePriceList(record.id);
        message.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除价目表失败', error);
        message.error('删除价目表失败');
      }
    };

    // 表单提交
    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        submitLoading.value = true;

        // 处理日期格式
        const formData = { ...form };
        if (formData.effectiveDate) {
          formData.effectiveDate = dayjs(formData.effectiveDate).format('YYYY-MM-DD');
        }

        if (form.id) {
          // 编辑
          await editPriceList(formData);
          message.success('修改成功');
        } else {
          // 新增
          await addPriceList(formData);
          message.success('新增成功');
        }

        open.value = false;
        getList();
      } catch (error) {
        console.error('提交失败', error);
        message.error('提交失败');
      } finally {
        submitLoading.value = false;
      }
    };

    // 取消操作
    const cancel = () => {
      open.value = false;
      resetForm();
    };

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields();
      }
      Object.assign(form, {
        id: undefined,
        parameter: undefined,
        method: undefined,
        samplingPrice: 0,
        testingPrice: 0,
        travelPrice: 0,
        effectiveDate: undefined,
        status: '0',
        remark: undefined
      });
    };

    // 初始化
    onMounted(() => {
      getList();
      getParameterOptions();
    });

    return {
      // 数据
      list,
      loading,
      pagination,
      queryParams,
      dateRange,
      parameterOptions,
      methodOptions,
      columns,

      // 表单
      formRef,
      open,
      title,
      submitLoading,
      form,
      rules,

      // 方法
      getList,
      handleParameterChange,
      handleDateRangeChange,
      handleFormParameterChange,
      handleQuery,
      resetQuery,
      handleTableChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleSubmit,
      cancel
    };
  }
});
</script>
