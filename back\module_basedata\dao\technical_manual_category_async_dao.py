from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, desc, asc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryQueryModel,
    TechnicalManualCategoryModel,
)
from utils.page_util import PageResponseModel


class TechnicalManualCategoryAsyncDao:
    """
    技术手册类目表异步数据访问层
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_by_id(self, category_id: int) -> Optional[TechnicalManualCategory]:
        """
        根据ID获取技术手册类目
        """
        stmt = select(TechnicalManualCategory).where(
            TechnicalManualCategory.id == category_id,
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def get_by_code(self, category_code: str) -> Optional[TechnicalManualCategory]:
        """
        根据类目编号获取技术手册类目
        """
        stmt = select(TechnicalManualCategory).where(
            TechnicalManualCategory.category_code == category_code,
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def get_by_classification_and_category(
        self, classification: str, category: str
    ) -> Optional[TechnicalManualCategory]:
        """
        根据分类和检测类别获取技术手册类目
        """
        stmt = select(TechnicalManualCategory).where(
            TechnicalManualCategory.classification == classification,
            TechnicalManualCategory.category == category,
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def get_next_category_code(self) -> str:
        """
        获取下一个类目编号
        """
        # 查询最大的类目编号
        stmt = select(func.max(TechnicalManualCategory.category_code))
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        if max_code:
            # 提取数字部分并加1
            try:
                num_part = int(max_code[4:])  # 去掉CATE前缀
                next_num = num_part + 1
            except (ValueError, IndexError):
                next_num = 1
        else:
            next_num = 1

        # 格式化为5位数字
        return f"CATE{next_num:05d}"

    async def create(self, category_data: TechnicalManualCategoryModel) -> TechnicalManualCategory:
        """
        创建技术手册类目
        """
        # 生成类目编号
        if not category_data.category_code:
            category_data.category_code = await self.get_next_category_code()

        category = TechnicalManualCategory(**category_data.model_dump(exclude_none=True))
        self.db.add(category)
        await self.db.flush()
        return category

    async def get_all_active(self) -> List[TechnicalManualCategory]:
        """
        获取所有有效的技术手册类目
        """
        stmt = (
            select(TechnicalManualCategory)
            .where(TechnicalManualCategory.status == "0")
            .order_by(TechnicalManualCategory.classification, TechnicalManualCategory.category)
        )

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_classifications(self) -> List[str]:
        """
        获取所有分类
        """
        stmt = select(TechnicalManualCategory.classification).where(TechnicalManualCategory.status == "0").distinct()

        result = await self.db.execute(stmt)
        classifications = result.scalars().all()

        return [item for item in classifications if item]

    async def get_categories_by_classification(self, classification: str) -> List[str]:
        """
        根据分类获取检测类别列表
        """
        stmt = select(TechnicalManualCategory.category).where(
            TechnicalManualCategory.classification == classification, TechnicalManualCategory.status == "0"
        )

        result = await self.db.execute(stmt)
        categories = result.scalars().all()

        return [item for item in categories if item]

    async def batch_create_if_not_exists(
        self, classification: str, categories: List[str]
    ) -> List[TechnicalManualCategory]:
        """
        批量创建类目（如果不存在）
        """
        created_categories = []

        for category in categories:
            # 检查是否已存在
            existing = await self.get_by_classification_and_category(classification, category)
            if not existing:
                # 创建新的类目
                category_data = TechnicalManualCategoryModel(
                    classification=classification, category=category, category_code=await self.get_next_category_code()
                )
                new_category = await self.create(category_data)
                created_categories.append(new_category)
            else:
                created_categories.append(existing)

        return created_categories

    async def get_category_code_by_classification_and_category(
        self, classification: str, category: str
    ) -> Optional[str]:
        """
        根据分类和检测类别获取类目编号
        """
        category_obj = await self.get_by_classification_and_category(classification, category)
        return category_obj.category_code if category_obj else None

    async def get_classification_and_category_by_code(self, category_code: str) -> Optional[Dict[str, str]]:
        """
        根据类目编号获取分类和检测类别
        """
        category = await self.get_by_code(category_code)
        if not category:
            return None

        return {"classification": category.classification, "category": category.category}

    async def get_category_codes_by_classification_and_categories(
        self, classification: str, categories: List[str]
    ) -> List[str]:
        """
        根据分类和检测类别列表获取类目编号列表
        """
        if not categories:
            return []

        stmt = select(TechnicalManualCategory.category_code).where(
            TechnicalManualCategory.classification == classification,
            TechnicalManualCategory.category.in_(categories),
        )

        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_category_map(self) -> Dict[str, Dict[str, str]]:
        """
        获取类目编号到分类和检测类别的映射
        """
        stmt = select(TechnicalManualCategory).where(TechnicalManualCategory.status == "0")

        result = await self.db.execute(stmt)
        categories = result.scalars().all()

        category_map = {}
        for category in categories:
            category_map[category.category_code] = {
                "classification": category.classification,
                "category": category.category,
            }

        return category_map
