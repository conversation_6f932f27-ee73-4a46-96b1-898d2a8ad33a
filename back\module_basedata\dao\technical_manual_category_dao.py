from typing import List, Optional
from sqlalchemy import and_, or_, desc, asc, func
from sqlalchemy.orm import Session

from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryQueryModel,
    TechnicalManualCategoryModel
)
from utils.page_util import PageResponseModel


class TechnicalManualCategoryDao:
    """
    技术手册类目表数据访问层
    """

    def __init__(self, db: Session):
        self.db = db

    def get_by_id(self, category_id: int) -> Optional[TechnicalManualCategory]:
        """
        根据ID获取技术手册类目
        """
        return self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.id == category_id,
            TechnicalManualCategory.del_flag == "0"
        ).first()

    def get_by_code(self, category_code: str) -> Optional[TechnicalManualCategory]:
        """
        根据类目编号获取技术手册类目
        """
        return self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.category_code == category_code,
            TechnicalManualCategory.del_flag == "0"
        ).first()

    def get_by_classification_and_category(self, classification: str, category: str) -> Optional[TechnicalManualCategory]:
        """
        根据分类和检测类别获取技术手册类目
        """
        return self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.classification == classification,
            TechnicalManualCategory.category == category,
            TechnicalManualCategory.del_flag == "0"
        ).first()

    def get_next_category_code(self) -> str:
        """
        获取下一个类目编号
        """
        # 查询最大的类目编号
        max_code = self.db.query(func.max(TechnicalManualCategory.category_code)).scalar()
        
        if max_code:
            # 提取数字部分并加1
            try:
                num_part = int(max_code[4:])  # 去掉CATE前缀
                next_num = num_part + 1
            except (ValueError, IndexError):
                next_num = 1
        else:
            next_num = 1
        
        # 格式化为5位数字
        return f"CATE{next_num:05d}"

    def create(self, category_data: TechnicalManualCategoryModel) -> TechnicalManualCategory:
        """
        创建技术手册类目
        """
        # 生成类目编号
        if not category_data.category_code:
            category_data.category_code = self.get_next_category_code()
        
        category = TechnicalManualCategory(**category_data.model_dump(exclude_none=True))
        self.db.add(category)
        self.db.flush()
        return category

    def update(self, category: TechnicalManualCategory, category_data: TechnicalManualCategoryModel) -> TechnicalManualCategory:
        """
        更新技术手册类目
        """
        update_data = category_data.model_dump(exclude_none=True, exclude={"id", "category_code"})
        for key, value in update_data.items():
            setattr(category, key, value)
        
        self.db.flush()
        return category

    def delete_by_ids(self, ids: List[int]) -> int:
        """
        批量删除技术手册类目（软删除）
        """
        count = self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.id.in_(ids),
            TechnicalManualCategory.del_flag == "0"
        ).update({"del_flag": "2"}, synchronize_session=False)
        
        self.db.flush()
        return count

    def get_list(self, query_model: TechnicalManualCategoryQueryModel) -> List[TechnicalManualCategory]:
        """
        获取技术手册类目列表
        """
        query = self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.del_flag == "0"
        )

        # 添加查询条件
        if query_model.classification:
            query = query.filter(TechnicalManualCategory.classification.like(f"%{query_model.classification}%"))
        
        if query_model.category:
            query = query.filter(TechnicalManualCategory.category.like(f"%{query_model.category}%"))
        
        if query_model.status:
            query = query.filter(TechnicalManualCategory.status == query_model.status)

        # 排序
        query = query.order_by(desc(TechnicalManualCategory.create_time))

        return query.all()

    def get_page(self, query_model: TechnicalManualCategoryQueryModel) -> PageResponseModel:
        """
        分页查询技术手册类目
        """
        query = self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.del_flag == "0"
        )

        # 添加查询条件
        if query_model.classification:
            query = query.filter(TechnicalManualCategory.classification.like(f"%{query_model.classification}%"))
        
        if query_model.category:
            query = query.filter(TechnicalManualCategory.category.like(f"%{query_model.category}%"))
        
        if query_model.status:
            query = query.filter(TechnicalManualCategory.status == query_model.status)

        # 总数
        total = query.count()

        # 分页
        offset = (query_model.page_num - 1) * query_model.page_size
        items = query.order_by(desc(TechnicalManualCategory.create_time)).offset(offset).limit(query_model.page_size).all()

        return PageResponseModel(
            total=total,
            rows=items,
            page_num=query_model.page_num,
            page_size=query_model.page_size
        )

    def get_all_active(self) -> List[TechnicalManualCategory]:
        """
        获取所有有效的技术手册类目
        """
        return self.db.query(TechnicalManualCategory).filter(
            TechnicalManualCategory.del_flag == "0",
            TechnicalManualCategory.status == "0"
        ).order_by(TechnicalManualCategory.classification, TechnicalManualCategory.category).all()

    def get_classifications(self) -> List[str]:
        """
        获取所有分类
        """
        result = self.db.query(TechnicalManualCategory.classification).filter(
            TechnicalManualCategory.del_flag == "0",
            TechnicalManualCategory.status == "0"
        ).distinct().all()
        
        return [item[0] for item in result if item[0]]

    def get_categories_by_classification(self, classification: str) -> List[str]:
        """
        根据分类获取检测类别列表
        """
        result = self.db.query(TechnicalManualCategory.category).filter(
            TechnicalManualCategory.classification == classification,
            TechnicalManualCategory.del_flag == "0",
            TechnicalManualCategory.status == "0"
        ).all()
        
        return [item[0] for item in result if item[0]]

    def batch_create_if_not_exists(self, classification: str, categories: List[str]) -> List[TechnicalManualCategory]:
        """
        批量创建类目（如果不存在）
        """
        created_categories = []
        
        for category in categories:
            # 检查是否已存在
            existing = self.get_by_classification_and_category(classification, category)
            if not existing:
                # 创建新的类目
                category_data = TechnicalManualCategoryModel(
                    classification=classification,
                    category=category,
                    category_code=self.get_next_category_code()
                )
                new_category = self.create(category_data)
                created_categories.append(new_category)
            else:
                created_categories.append(existing)
        
        return created_categories
