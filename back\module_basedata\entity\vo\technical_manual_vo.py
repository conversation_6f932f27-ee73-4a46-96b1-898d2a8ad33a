from datetime import datetime
from typing import List, Optional, Literal

from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank, Size

from module_admin.annotation.pydantic_annotation import as_query


class TechnicalManualModel(BaseModel):
    """
    技术手册模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='主键ID')
    test_code: Optional[str] = Field(default=None, description='检测编号', max_length=20)
    category: str = Field(..., description='检测类别', max_length=50)
    parameter: str = Field(..., description='检测参数', max_length=50)
    method: str = Field(..., description='检测方法', max_length=100)
    description: Optional[str] = Field(default=None, description='技术描述')
    status: Optional[Literal['0', '1']] = Field(default='0', description='状态（0正常 1停用）')
    del_flag: Optional[Literal['0', '2']] = Field(default='0', description='删除标志（0存在 2删除）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')

    @NotBlank(field_name='category', message='检测类别不能为空')
    @Size(field_name='category', min_length=0, max_length=50, message='检测类别长度不能超过50个字符')
    def get_category(self):
        return self.category

    @NotBlank(field_name='parameter', message='检测参数不能为空')
    @Size(field_name='parameter', min_length=0, max_length=50, message='检测参数长度不能超过50个字符')
    def get_parameter(self):
        return self.parameter

    @NotBlank(field_name='method', message='检测方法不能为空')
    @Size(field_name='method', min_length=0, max_length=100, message='检测方法长度不能超过100个字符')
    def get_method(self):
        return self.method

    def validate_fields(self):
        self.get_category()
        self.get_parameter()
        self.get_method()


class AddTechnicalManualModel(TechnicalManualModel):
    """
    新增技术手册模型
    """
    id: Optional[int] = Field(default=None, exclude=True, description='主键ID')


class EditTechnicalManualModel(TechnicalManualModel):
    """
    编辑技术手册模型
    """
    id: int = Field(..., description='主键ID')


@as_query
class TechnicalManualQueryModel(BaseModel):
    """
    技术手册查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    category: Optional[str] = Field(default=None, description='检测类别')
    parameter: Optional[str] = Field(default=None, description='检测参数')
    method: Optional[str] = Field(default=None, description='检测方法')
    keyword: Optional[str] = Field(default=None, description='关键词')
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class TechnicalManualPageQueryModel(TechnicalManualQueryModel):
    """
    技术手册分页查询模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class BatchUpdateTechnicalManualModel(BaseModel):
    """
    批量更新技术手册模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    ids: List[int] = Field(..., description='技术手册ID列表')
    point_count: Optional[int] = Field(default=None, description='点位数')
    cycle_type: Optional[str] = Field(default=None, description='周期类型')
    cycle_count: Optional[int] = Field(default=None, description='周期数')
    frequency: Optional[int] = Field(default=None, description='频次数')
    sample_count: Optional[int] = Field(default=None, description='样品数')
    service_type: Optional[str] = Field(default=None, description='服务类型（采样检测,送样检测）')