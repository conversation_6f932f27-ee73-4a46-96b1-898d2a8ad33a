from datetime import datetime, date
from typing import List, Optional, Literal
from decimal import Decimal

from pydantic import BaseModel, Field, ConfigDict, field_validator
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import NotBlank, Size

from module_admin.annotation.pydantic_annotation import as_query


class TechnicalManualModel(BaseModel):
    """
    技术手册模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    test_code: Optional[str] = Field(default=None, description="检测编号", max_length=20)
    category_code: Optional[str] = Field(default=None, description="类目编号（已废弃）", max_length=20)
    category_codes: Optional[List[str]] = Field(default=None, description="类目编码列表")
    parameter: str = Field(..., description="检测参数", max_length=50)
    method: str = Field(..., description="检测方法", max_length=100)
    description: Optional[str] = Field(default=None, description="技术描述")
    # 新增字段
    qualification_code: Optional[str] = Field(default=None, description="资质编号", max_length=50)
    limitation_scope: Optional[str] = Field(default=None, description="限制范围")
    common_alias: Optional[str] = Field(default=None, description="常用别名（已废弃）", max_length=200)
    alias_list: Optional[List[str]] = Field(default=None, description="别名列表")
    qualification_date: Optional[date] = Field(default=None, description="取得资质时间")
    has_qualification: Optional[Literal["0", "1"]] = Field(default="0", description="是否有资质(0=有，1=无)")
    # 从技术手册价格表迁移过来的字段
    special_consumables_price: Optional[float] = Field(default=None, description="分析特殊耗材单价")
    # 关联查询字段（不存储在数据库中，仅用于前端显示）
    classification: str = Field(..., description="分类（关联查询）")
    category: str = Field(..., description="检测类别（关联查询）")
    # 原有字段
    status: Optional[Literal["0", "1"]] = Field(default="0", description="状态（0正常 1停用）")
    # del_flag: Optional[Literal["0", "2"]] = Field(default="0", description="删除标志（0存在 2删除）")
    create_by: Optional[str] = Field(default=None, description="创建者")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新者")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")
    remark: Optional[str] = Field(default=None, description="备注")

    def validate_category_codes(self):
        """验证类目编码列表"""
        if not self.category_codes or len(self.category_codes) == 0:
            raise ValueError("类目编码列表不能为空")
        return self.category_codes

    @NotBlank(field_name="parameter", message="检测参数不能为空")
    @Size(field_name="parameter", min_length=0, max_length=50, message="检测参数长度不能超过50个字符")
    def get_parameter(self):
        return self.parameter

    @NotBlank(field_name="method", message="检测方法不能为空")
    @Size(field_name="method", min_length=0, max_length=100, message="检测方法长度不能超过100个字符")
    def get_method(self):
        return self.method

    def validate_fields(self):
        self.get_category_code()
        self.get_parameter()
        self.get_method()

    @field_validator("qualification_code")
    @classmethod
    def validate_qualification_code(cls, v):
        if v and len(v.strip()) == 0:
            return None
        return v


class AddTechnicalManualModel(TechnicalManualModel):
    """
    新增技术手册模型
    """

    id: Optional[int] = Field(default=None, exclude=True, description="主键ID")


class EditTechnicalManualModel(TechnicalManualModel):
    """
    编辑技术手册模型
    """

    id: int = Field(..., description="主键ID")


@as_query
class TechnicalManualQueryModel(BaseModel):
    """
    技术手册查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    classification: Optional[str] = Field(default=None, description="分类")
    category: Optional[str] = Field(default=None, description="检测类别")
    parameter: Optional[str] = Field(default=None, description="检测参数")
    method: Optional[str] = Field(default=None, description="检测方法")
    qualificationCode: Optional[str] = Field(default=None, description="资质编号")
    alias: Optional[str] = Field(default=None, description="别名查询")
    keyword: Optional[str] = Field(default=None, description="关键词")
    begin_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")


@as_query
class TechnicalManualPageQueryModel(TechnicalManualQueryModel):
    """
    技术手册分页查询模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    page_num: int = Field(default=1, description="当前页码")
    page_size: int = Field(default=10, description="每页记录数")


class BatchInputTechnicalManualModel(BaseModel):
    """
    批量录入技术手册模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    classification: str = Field(..., description="分类")
    categories: str = Field(..., description="检测类别，多个用逗号分隔")
    parameters: str = Field(..., description="检测参数，多个用逗号分隔")
    methods: str = Field(..., description="检测方法，多个用逗号分隔")
    # 新增字段
    qualification_code: Optional[str] = Field(default=None, description="资质编号")
    limitation_scope: Optional[str] = Field(default=None, description="限制范围")
    common_alias: Optional[str] = Field(default=None, description="常用别名（已废弃）")
    alias_list: Optional[List[str]] = Field(default=None, description="别名列表")
    qualification_date: Optional[date] = Field(default=None, description="取得资质时间")
    has_qualification: Optional[Literal["0", "1"]] = Field(default="0", description="是否有资质(0=有，1=无)")
    special_consumables_price: Optional[float] = Field(default=None, description="分析特殊耗材单价")
    # 原有字段
    status: Optional[Literal["0", "1"]] = Field(default="0", description="状态（0正常 1停用）")
    remark: Optional[str] = Field(default=None, description="备注")


class BatchUpdateTechnicalManualModel(BaseModel):
    """
    批量更新技术手册模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    ids: List[int] = Field(..., description="技术手册ID列表")
    # 新增字段
    qualification_code: Optional[str] = Field(default=None, description="资质编号")
    limitation_scope: Optional[str] = Field(default=None, description="限制范围")
    common_alias: Optional[str] = Field(default=None, description="常用别名（已废弃）")
    alias_list: Optional[List[str]] = Field(default=None, description="别名列表")
    qualification_date: Optional[date] = Field(default=None, description="取得资质时间")
    has_qualification: Optional[Literal["0", "1"]] = Field(default="0", description="是否有资质(0=有，1=无)")
    special_consumables_price: Optional[float] = Field(default=None, description="分析特殊耗材单价")
    # 原有字段
    status: Optional[Literal["0", "1"]] = Field(default="0", description="状态（0正常 1停用）")
    remark: Optional[str] = Field(default=None, description="备注")


class TechnicalManualImportModel(BaseModel):
    """
    技术手册导入模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    classification: str = Field(..., description="分类")
    category: str = Field(..., description="检测类别")
    parameter: str = Field(..., description="检测参数")
    method: str = Field(..., description="检测方法")
    qualification_code: Optional[str] = Field(default=None, description="资质编号")
    limitation_scope: Optional[str] = Field(default=None, description="限制范围")
    common_alias: Optional[str] = Field(default=None, description="常用别名（已废弃）")
    alias_list: Optional[str] = Field(default=None, description="别名列表（逗号分隔）")
    has_qualification: Optional[str] = Field(default="0", description="是否有资质")
    qualification_date: Optional[str] = Field(default=None, description="取得资质时间")
    special_consumables_price: Optional[str] = Field(default=None, description="分析特殊耗材单价")


class TechnicalManualImportErrorModel(BaseModel):
    """
    技术手册导入错误模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    row_number: Optional[int] = Field(default=0, description="错误行号")
    error_message: Optional[str] = Field(None, description="错误原因")
    qualification_code: Optional[str] = Field(default=None, description="资质编号")
    category: Optional[str] = Field(default=None, description="检测类别")
    parameter: Optional[str] = Field(default=None, description="检测参数")
    method: Optional[str] = Field(default=None, description="检测方法")


class TechnicalManualImportResultModel(BaseModel):
    """
    技术手册导入结果模型
    """

    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    success: Optional[bool] = Field(default=True, description="是否成功")
    total_count: Optional[int] = Field(default=0, description="总记录数")
    success_count: Optional[int] = Field(default=0, description="成功记录数")
    error_count: Optional[int] = Field(default=0, description="错误记录数")
    errors: List[TechnicalManualImportErrorModel] = Field(default=[], description="错误列表")
