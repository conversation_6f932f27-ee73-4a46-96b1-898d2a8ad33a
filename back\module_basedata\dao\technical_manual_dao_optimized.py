from datetime import datetime, time
from sqlalchemy import and_, or_, select, distinct
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_dao import BaseDao
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.technical_manual_vo import TechnicalManualPageQueryModel, TechnicalManualQueryModel
from utils.page_util import PageUtil


class TechnicalManualDao(BaseDao[TechnicalManual]):
    """
    技术手册管理模块数据库操作层
    """

    def __init__(self, db: AsyncSession):
        """
        初始化技术手册数据访问对象

        :param db: 数据库会话
        """
        super().__init__(TechnicalManual, db)
        self.db = db

    async def get_technical_manual_list(self, query_object: TechnicalManualQueryModel):
        """
        获取技术手册列表

        :param query_object: 查询参数对象
        :return: 技术手册列表
        """
        # 构建查询条件
        conditions = [TechnicalManual.del_flag == '0']

        if query_object.category:
            conditions.append(TechnicalManual.category == query_object.category)
        if query_object.parameter:
            conditions.append(TechnicalManual.parameter == query_object.parameter)
        if query_object.method:
            conditions.append(TechnicalManual.method == query_object.method)
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManual.category.like(f'%{query_object.keyword}%'),
                TechnicalManual.parameter.like(f'%{query_object.keyword}%'),
                TechnicalManual.method.like(f'%{query_object.keyword}%'),
                TechnicalManual.standard.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            time_condition = and_(
                TechnicalManual.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManual.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        query = select(TechnicalManual).where(and_(*conditions)).order_by(
            TechnicalManual.category, TechnicalManual.parameter, TechnicalManual.method
        )
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_technical_manual_page(self, query_object: TechnicalManualPageQueryModel):
        """
        获取技术手册分页列表

        :param query_object: 查询参数对象
        :return: 技术手册分页列表
        """
        # 构建查询条件
        conditions = [TechnicalManual.del_flag == '0']

        if query_object.category:
            conditions.append(TechnicalManual.category == query_object.category)
        if query_object.parameter:
            conditions.append(TechnicalManual.parameter == query_object.parameter)
        if query_object.method:
            conditions.append(TechnicalManual.method == query_object.method)
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManual.category.like(f'%{query_object.keyword}%'),
                TechnicalManual.parameter.like(f'%{query_object.keyword}%'),
                TechnicalManual.method.like(f'%{query_object.keyword}%'),
                TechnicalManual.standard.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            time_condition = and_(
                TechnicalManual.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManual.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        query = select(TechnicalManual).where(and_(*conditions)).order_by(
            TechnicalManual.category, TechnicalManual.parameter, TechnicalManual.method
        )
        result = await PageUtil.paginate(self.db, query, query_object.page_num, query_object.page_size)
        return result

    async def check_technical_manual_unique(self, category: str, parameter: str, method: str, id: int = None):
        """
        检查技术手册是否唯一

        :param category: 检测类别
        :param parameter: 检测参数
        :param method: 检测方法
        :param id: 技术手册ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManual.category == category,
            TechnicalManual.parameter == parameter,
            TechnicalManual.method == method,
            TechnicalManual.del_flag == '0'
        ]

        if id:
            conditions.append(TechnicalManual.id != id)

        result = await self.orm_client.list_all_with_condition(and_(*conditions))
        return len(result) == 0

    async def add_technical_manual(self, technical_manual: TechnicalManual):
        """
        新增技术手册

        :param technical_manual: 技术手册对象
        :return: 新增的技术手册对象
        """
        # 使用 __dict__ 获取对象的所有属性
        data = {k: v for k, v in technical_manual.__dict__.items() if not k.startswith('_')}
        return await self.create(data)

    async def update_technical_manual(self, technical_manual: TechnicalManual):
        """
        更新技术手册

        :param technical_manual: 技术手册对象
        :return: 更新结果
        """
        # 使用 __dict__ 获取对象的所有属性，排除 id 和私有属性
        data = {k: v for k, v in technical_manual.__dict__.items() if not k.startswith('_') and k != 'id'}
        result = await self.update(technical_manual.id, data)
        return 1 if result else 0

    async def delete_technical_manual(self, id: int, update_by: str):
        """
        删除技术手册

        :param id: 技术手册ID
        :param update_by: 更新人
        :return: 删除结果
        """
        result = await self.soft_delete(id, update_by)
        return 1 if result else 0

    async def get_categories(self):
        """
        获取所有检测类别

        :return: 检测类别列表
        """
        query = select(distinct(TechnicalManual.category)).where(TechnicalManual.del_flag == '0')
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_parameters(self, category: str = None):
        """
        获取检测参数列表

        :param category: 检测类别
        :return: 检测参数列表
        """
        conditions = [TechnicalManual.del_flag == '0']
        if category:
            conditions.append(TechnicalManual.category == category)

        query = select(distinct(TechnicalManual.parameter)).where(and_(*conditions))
        result = await self.db.execute(query)
        return result.scalars().all()

    async def get_methods(self, category: str = None, parameter: str = None):
        """
        获取检测方法列表

        :param category: 检测类别
        :param parameter: 检测参数
        :return: 检测方法列表
        """
        conditions = [TechnicalManual.del_flag == '0']
        if category:
            conditions.append(TechnicalManual.category == category)
        if parameter:
            conditions.append(TechnicalManual.parameter == parameter)

        query = select(distinct(TechnicalManual.method)).where(and_(*conditions))
        result = await self.db.execute(query)
        return result.scalars().all()
