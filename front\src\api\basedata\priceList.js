import request from '@/utils/request'

// 查询价目表列表
export function listPriceList(query) {
  return request({
    url: '/basedata/price-list/list',
    method: 'get',
    params: query
  })
}

// 查询价目表分页列表
export function pagePriceList(query) {
  return request({
    url: '/basedata/price-list/page',
    method: 'get',
    params: query
  })
}

// 查询价目表详细
export function getPriceList(id) {
  return request({
    url: '/basedata/price-list/' + id,
    method: 'get'
  })
}

// 新增价目表
export function addPriceList(data) {
  return request({
    url: '/basedata/price-list',
    method: 'post',
    data: data
  })
}

// 修改价目表
export function updatePriceList(data) {
  return request({
    url: '/basedata/price-list',
    method: 'put',
    data: data
  })
}

// 删除价目表
export function delPriceList(id) {
  return request({
    url: '/basedata/price-list/' + id,
    method: 'delete'
  })
}

// 获取检测参数选项
export function getParameterOptions() {
  return request({
    url: '/basedata/price-list/options/parameters',
    method: 'get'
  })
}

// 获取检测方法选项
export function getMethodOptions(parameter) {
  return request({
    url: '/basedata/price-list/options/methods',
    method: 'get',
    params: { parameter }
  })
}

// 根据检测编号查询价目表
export function getPriceListByTestCode(testCode) {
  return request({
    url: '/basedata/price-list/by-test-code/' + testCode,
    method: 'get'
  })
}
