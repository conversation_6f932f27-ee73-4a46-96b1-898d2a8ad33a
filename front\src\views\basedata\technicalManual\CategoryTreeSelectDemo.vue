<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>检测类别树形选择组件演示</span>
        </div>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>功能说明</h3>
            <ul>
              <li>支持2级树形结构：一级为分类，二级为检测类别</li>
              <li>只有二级（检测类别）支持多选</li>
              <li>支持输入框搜索检测类别</li>
              <li>点击确定后向父组件传递选中的检测类别</li>
            </ul>
            
            <el-button type="primary" @click="openCategorySelect" style="margin-top: 20px;">
              打开检测类别选择器
            </el-button>
          </el-col>
          
          <el-col :span="12">
            <h3>选择结果</h3>
            <div v-if="selectedCategories.length > 0" class="selected-result">
              <p><strong>已选择的检测类别（{{ selectedCategories.length }}个）：</strong></p>
              <el-tag
                v-for="category in selectedCategories"
                :key="category"
                style="margin-right: 8px; margin-bottom: 8px;"
                type="success"
              >
                {{ category }}
              </el-tag>
            </div>
            <div v-else class="no-selection">
              <el-empty description="暂未选择任何检测类别" :image-size="100" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 检测类别选择组件 -->
    <category-tree-select
      :visible="categorySelectOpen"
      @update:visible="categorySelectOpen = $event"
      :selected-values="selectedCategories"
      @confirm="handleCategoryConfirm"
      title="选择检测类别"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import CategoryTreeSelect from '@/components/CategoryTreeSelect/index.vue'

// 是否显示检测类别选择弹出层
const categorySelectOpen = ref(false)
// 已选择的检测类别
const selectedCategories = ref([])

/** 打开检测类别选择器 */
function openCategorySelect() {
  categorySelectOpen.value = true
}

/** 处理检测类别选择确认 */
function handleCategoryConfirm(categories) {
  selectedCategories.value = [...categories]
  
  // 显示选择结果
  if (categories.length > 0) {
    ElMessage.success(`已选择 ${categories.length} 个检测类别`)
    console.log('选中的检测类别:', categories)
  } else {
    ElMessage.info('未选择任何检测类别')
  }
}
</script>

<style scoped>
.demo-content {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-result {
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  min-height: 100px;
}

.no-selection {
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

h3 {
  color: #303133;
  margin-bottom: 15px;
}

ul {
  color: #606266;
  line-height: 1.6;
}

li {
  margin-bottom: 8px;
}
</style>
