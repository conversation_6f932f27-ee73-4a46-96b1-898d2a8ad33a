<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>报价单详情</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>
      
      <div v-loading="loading">
        <!-- 报价基本信息 -->
        <el-collapse v-model="activeNames">
          <el-collapse-item name="1">
            <template slot="title">
              <h3>报价基本信息</h3>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="报价编号">{{ quotation.quotationNo }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="statusTypeMap[quotation.status]">{{ statusLabelMap[quotation.status] }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="客户名称">{{ quotation.customerName }}</el-descriptions-item>
              <el-descriptions-item label="客户地址">{{ quotation.customerAddress }}</el-descriptions-item>
              <el-descriptions-item label="联系人">{{ quotation.contactName }}</el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ quotation.contactPhone }}</el-descriptions-item>
              <el-descriptions-item label="受检方">{{ quotation.inspectedParty }}</el-descriptions-item>
              <el-descriptions-item label="受检方地址">{{ quotation.inspectedAddress }}</el-descriptions-item>
              <el-descriptions-item label="受检方联系人">{{ quotation.inspectedContact }}</el-descriptions-item>
              <el-descriptions-item label="受检方电话">{{ quotation.inspectedPhone }}</el-descriptions-item>
              <el-descriptions-item label="总金额">{{ quotation.totalAmount }} 元</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(quotation.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">{{ quotation.remark }}</el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          
          <!-- 报价明细信息 -->
          <el-collapse-item name="2">
            <template slot="title">
              <h3>报价明细信息</h3>
            </template>
            
            <!-- 搜索框 -->
            <el-row :gutter="20" style="margin-bottom: 15px;">
              <el-col :span="6">
                <el-input
                  placeholder="请输入检测参数搜索"
                  v-model="itemSearchKeyword"
                  clearable
                  @keyup.enter.native="filterItems"
                >
                  <el-button slot="append" icon="el-icon-search" @click="filterItems"></el-button>
                </el-input>
              </el-col>
            </el-row>
            
            <!-- 项目表格 -->
            <el-table :data="filteredItems" border style="width: 100%">
              <el-table-column label="检测参数" prop="parameter"></el-table-column>
              <el-table-column label="检测方法" prop="method"></el-table-column>
              <el-table-column label="点位数" prop="pointCount" width="80" align="center"></el-table-column>
              <el-table-column label="周期类型" prop="cycleType" width="100"></el-table-column>
              <el-table-column label="周期数" prop="cycleCount" width="80" align="center"></el-table-column>
              <el-table-column label="频次数" prop="frequency" width="80" align="center"></el-table-column>
              <el-table-column label="样品数" prop="sampleCount" width="80" align="center"></el-table-column>
              <el-table-column label="单价" prop="unitPrice" width="100" align="right">
                <template slot-scope="scope">
                  {{ scope.row.unitPrice }} 元
                </template>
              </el-table-column>
              <el-table-column label="金额" prop="amount" width="120" align="right">
                <template slot-scope="scope">
                  {{ scope.row.amount }} 元
                </template>
              </el-table-column>
              <el-table-column label="备注" prop="remark" show-overflow-tooltip></el-table-column>
            </el-table>
            
            <!-- 总金额 -->
            <div style="margin-top: 15px; text-align: right;">
              <span style="font-weight: bold;">总金额：{{ quotation.totalAmount }} 元</span>
            </div>
          </el-collapse-item>
          
          <!-- 附件信息 -->
          <el-collapse-item name="3">
            <template slot="title">
              <h3>附件信息</h3>
            </template>
            <el-table :data="quotation.attachments" border style="width: 100%">
              <el-table-column label="文件名" prop="fileName"></el-table-column>
              <el-table-column label="文件类型" prop="fileType" width="150"></el-table-column>
              <el-table-column label="文件大小" prop="fileSize" width="120">
                <template slot-scope="scope">
                  {{ formatFileSize(scope.row.fileSize) }}
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="createTime" width="180">
                <template slot-scope="scope">
                  {{ parseTime(scope.row.createTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-download"
                    @click="downloadFile(scope.row)"
                  >下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-collapse-item>
          
          <!-- 操作日志 -->
          <el-collapse-item name="4">
            <template slot="title">
              <h3>操作日志</h3>
            </template>
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in operationLogs"
                :key="index"
                :timestamp="parseTime(log.operationTime)"
                :type="getTimelineItemType(log.operationType)"
              >
                <h4>{{ log.operationType }}</h4>
                <p>操作人：{{ log.operationBy }}</p>
                <p v-if="log.remark">备注：{{ log.remark }}</p>
              </el-timeline-item>
            </el-timeline>
          </el-collapse-item>
        </el-collapse>
        
        <!-- 操作按钮 -->
        <div style="margin-top: 20px; text-align: center;">
          <el-button type="primary" @click="handleEdit" v-if="quotation.status === '0' || quotation.status === '3' || quotation.status === '4'">编辑</el-button>
          <el-button type="success" @click="handleSubmit" v-if="quotation.status === '0' || quotation.status === '3' || quotation.status === '4'">提交审核</el-button>
          <el-button type="warning" @click="handleAudit" v-if="quotation.status === '1'">审核</el-button>
          <el-button type="info" @click="handleWithdraw" v-if="quotation.status === '1'">撤回</el-button>
          <el-button type="danger" @click="handleDelete" v-if="quotation.status === '0' || quotation.status === '3' || quotation.status === '4'">删除</el-button>
          <el-button @click="goBack">返回</el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 提交对话框 -->
    <el-dialog title="提交报价单" :visible.sync="submitOpen" width="500px" append-to-body>
      <el-form ref="submitForm" :model="submitForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="submitForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitQuotation">确 定</el-button>
        <el-button @click="submitOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核报价单" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio label="2">通过</el-radio>
            <el-radio label="4">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="auditQuotation">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 撤回对话框 -->
    <el-dialog title="撤回报价单" :visible.sync="withdrawOpen" width="500px" append-to-body>
      <el-form ref="withdrawForm" :model="withdrawForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="withdrawForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="withdrawQuotation">确 定</el-button>
        <el-button @click="withdrawOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getQuotationDetail, 
  getOperationLogs, 
  deleteQuotation, 
  submitQuotation, 
  auditQuotation, 
  withdrawQuotation 
} from "@/api/quotation/quotation";

export default {
  name: "QuotationDetail",
  data() {
    return {
      // 加载状态
      loading: false,
      // 折叠面板激活项
      activeNames: ['1', '2'],
      // 报价单信息
      quotation: {
        id: undefined,
        quotationNo: "",
        customerId: undefined,
        customerName: "",
        customerAddress: "",
        contactName: "",
        contactPhone: "",
        inspectedParty: "",
        inspectedContact: "",
        inspectedPhone: "",
        inspectedAddress: "",
        totalAmount: 0,
        status: "0",
        remark: "",
        items: [],
        attachments: []
      },
      // 操作日志
      operationLogs: [],
      // 状态类型映射
      statusTypeMap: {
        "0": "info",
        "1": "warning",
        "2": "success",
        "3": "info",
        "4": "danger"
      },
      // 状态标签映射
      statusLabelMap: {
        "0": "草稿",
        "1": "待审核",
        "2": "已审核",
        "3": "已撤回",
        "4": "已拒绝"
      },
      // 项目搜索关键字
      itemSearchKeyword: "",
      // 提交表单参数
      submitForm: {
        id: undefined,
        remark: undefined
      },
      // 提交对话框是否显示
      submitOpen: false,
      // 审核表单参数
      auditForm: {
        id: undefined,
        status: "2",
        remark: undefined
      },
      // 审核对话框是否显示
      auditOpen: false,
      // 撤回表单参数
      withdrawForm: {
        id: undefined,
        remark: undefined
      },
      // 撤回对话框是否显示
      withdrawOpen: false
    };
  },
  computed: {
    // 过滤后的项目列表
    filteredItems() {
      if (!this.itemSearchKeyword) {
        return this.quotation.items;
      }
      return this.quotation.items.filter(item => 
        item.parameter.toLowerCase().includes(this.itemSearchKeyword.toLowerCase())
      );
    }
  },
  created() {
    const id = this.$route.params && this.$route.params.id;
    if (id) {
      this.getDetail(id);
      this.getOperationLogs(id);
    }
  },
  methods: {
    /** 获取报价单详情 */
    getDetail(id) {
      this.loading = true;
      getQuotationDetail(id).then(response => {
        this.quotation = response.data;
        this.loading = false;
      });
    },
    /** 获取操作日志 */
    getOperationLogs(quotationId) {
      getOperationLogs(quotationId).then(response => {
        this.operationLogs = response.data;
      });
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else if (size < 1024 * 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      } else {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
      }
    },
    /** 下载文件 */
    downloadFile(file) {
      window.open(process.env.VUE_APP_BASE_API + '/' + file.filePath);
    },
    /** 获取时间线项目类型 */
    getTimelineItemType(operationType) {
      if (operationType === '新增' || operationType === '修改') {
        return 'primary';
      } else if (operationType === '提交审核') {
        return 'warning';
      } else if (operationType === '审核通过') {
        return 'success';
      } else if (operationType === '审核拒绝' || operationType === '删除') {
        return 'danger';
      } else {
        return 'info';
      }
    },
    /** 过滤项目 */
    filterItems() {
      // 已在计算属性中实现
    },
    /** 编辑按钮操作 */
    handleEdit() {
      this.$router.push({ path: `/quotation/edit/${this.quotation.id}` });
    },
    /** 提交按钮操作 */
    handleSubmit() {
      this.submitForm = {
        id: this.quotation.id,
        remark: undefined
      };
      this.submitOpen = true;
    },
    /** 提交报价单 */
    submitQuotation() {
      submitQuotation(this.submitForm).then(response => {
        this.$modal.msgSuccess("提交成功");
        this.submitOpen = false;
        this.getDetail(this.quotation.id);
        this.getOperationLogs(this.quotation.id);
      });
    },
    /** 审核按钮操作 */
    handleAudit() {
      this.auditForm = {
        id: this.quotation.id,
        status: "2",
        remark: undefined
      };
      this.auditOpen = true;
    },
    /** 审核报价单 */
    auditQuotation() {
      auditQuotation(this.auditForm).then(response => {
        this.$modal.msgSuccess("审核成功");
        this.auditOpen = false;
        this.getDetail(this.quotation.id);
        this.getOperationLogs(this.quotation.id);
      });
    },
    /** 撤回按钮操作 */
    handleWithdraw() {
      this.withdrawForm = {
        id: this.quotation.id,
        remark: undefined
      };
      this.withdrawOpen = true;
    },
    /** 撤回报价单 */
    withdrawQuotation() {
      withdrawQuotation(this.withdrawForm).then(response => {
        this.$modal.msgSuccess("撤回成功");
        this.withdrawOpen = false;
        this.getDetail(this.quotation.id);
        this.getOperationLogs(this.quotation.id);
      });
    },
    /** 删除按钮操作 */
    handleDelete() {
      this.$modal.confirm('是否确认删除该报价单？').then(() => {
        return deleteQuotation(this.quotation.id);
      }).then(() => {
        this.$modal.msgSuccess("删除成功");
        this.goBack();
      }).catch(() => {});
    },
    /** 返回按钮操作 */
    goBack() {
      this.$router.push({ path: "/quotation" });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
