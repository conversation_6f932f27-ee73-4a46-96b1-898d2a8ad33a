"""
技术手册批量导入服务
"""

import io
import re
from datetime import datetime, date
from typing import List, Dict, Any, Optional
import pandas as pd
from fastapi import UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.technical_manual_vo import (
    TechnicalManualImportModel,
    TechnicalManualImportErrorModel,
    TechnicalManualImportResultModel,
)
from utils.common_util import CamelCaseUtil


class TechnicalManualImportService:
    """
    技术手册批量导入服务
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def import_from_excel(
        self, file: UploadFile, current_user: CurrentUserModel
    ) -> TechnicalManualImportResultModel:
        """
        从Excel文件导入技术手册数据

        :param file: 上传的Excel文件
        :param current_user: 当前用户
        :return: 导入结果
        """
        try:
            # 读取Excel文件
            content = await file.read()
            df = pd.read_excel(io.BytesIO(content))

            # 验证Excel格式
            required_columns = [
                "分类",
                "检测类别",
                "检测参数",
                "检测方法",
                "资质编号",
                "限制范围",
                "常用别名",
                "是否资质",
                "取得资质时间",
            ]

            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return TechnicalManualImportResultModel(
                    success=False,
                    total_count=0,
                    success_count=0,
                    error_count=1,
                    errors=[
                        TechnicalManualImportErrorModel(
                            row_number=0,
                            error_message=f"Excel模板格式错误，缺少列：{', '.join(missing_columns)}",
                            qualification_code=None,
                            category=None,
                            parameter=None,
                            method=None,
                        )
                    ],
                )

            # 处理数据
            total_count = len(df)
            success_count = 0
            errors = []

            for index, row in df.iterrows():
                try:
                    # 验证和转换数据
                    import_data = await self._validate_and_convert_row(row, index + 2)  # +2因为Excel从第2行开始

                    # 检查是否已存在相同的记录
                    existing = await self._check_existing_record(
                        import_data.category, import_data.parameter, import_data.method
                    )

                    if existing:
                        errors.append(
                            TechnicalManualImportErrorModel(
                                row_number=index + 2,
                                error_message="记录已存在（相同的检测类别、参数和方法）",
                                qualification_code=import_data.qualification_code,
                                category=import_data.category,
                                parameter=import_data.parameter,
                                method=import_data.method,
                            )
                        )
                        continue

                    # 创建技术手册记录
                    technical_manual = TechnicalManual(
                        classification=import_data.classification,
                        category=import_data.category,
                        parameter=import_data.parameter,
                        method=import_data.method,
                        qualification_code=import_data.qualification_code,
                        limitation_scope=import_data.limitation_scope,
                        common_alias=import_data.common_alias,
                        has_qualification=import_data.has_qualification,
                        qualification_date=self._parse_date(import_data.qualification_date),
                        status="0",
                        del_flag="0",
                        create_by=current_user.user.user_name,
                        create_time=datetime.now(),
                        update_by=current_user.user.user_name,
                        update_time=datetime.now(),
                    )

                    self.db.add(technical_manual)
                    success_count += 1

                except Exception as e:
                    errors.append(
                        TechnicalManualImportErrorModel(
                            row_number=index + 2,
                            error_message=str(e),
                            qualification_code=str(row.get("资质编号", "")),
                            category=str(row.get("检测类别", "")),
                            parameter=str(row.get("检测参数", "")),
                            method=str(row.get("检测方法", "")),
                        )
                    )

            # 如果有成功的记录，提交事务
            if success_count > 0:
                await self.db.commit()

            return TechnicalManualImportResultModel(
                success=len(errors) == 0,
                total_count=total_count,
                success_count=success_count,
                error_count=len(errors),
                errors=errors,
            )

        except Exception as e:
            await self.db.rollback()
            return CamelCaseUtil.transform_result(
                TechnicalManualImportResultModel(
                    success=False,
                    total_count=0,
                    success_count=0,
                    error_count=1,
                    errors=[
                        TechnicalManualImportErrorModel(
                            row_number=0,
                            error_message=f"文件处理失败：{str(e)}",
                            qualification_code=None,
                            category=None,
                            parameter=None,
                            method=None,
                        )
                    ],
                )
            )

    async def _validate_and_convert_row(self, row: pd.Series, row_number: int) -> TechnicalManualImportModel:
        """
        验证和转换行数据

        :param row: 行数据
        :param row_number: 行号
        :return: 转换后的数据模型
        """
        # 必填字段验证
        if pd.isna(row["检测类别"]) or str(row["检测类别"]).strip() == "":
            raise ValueError("检测类别不能为空")

        if pd.isna(row["检测参数"]) or str(row["检测参数"]).strip() == "":
            raise ValueError("检测参数不能为空")

        if pd.isna(row["检测方法"]) or str(row["检测方法"]).strip() == "":
            raise ValueError("检测方法不能为空")

        # 是否资质字段验证
        has_qualification = str(row.get("是否资质", "有")).strip()
        if has_qualification in ["有", "0"]:
            has_qualification = "0"
        elif has_qualification in ["无", "1"]:
            has_qualification = "1"
        else:
            raise ValueError("是否资质字段值无效，应为：有/无 或 0/1")

        return TechnicalManualImportModel(
            classification=str(row.get("分类", "")).strip() if not pd.isna(row.get("分类")) else None,
            category=str(row["检测类别"]).strip(),
            parameter=str(row["检测参数"]).strip(),
            method=str(row["检测方法"]).strip(),
            qualification_code=str(row.get("资质编号", "")).strip() if not pd.isna(row.get("资质编号")) else None,
            limitation_scope=str(row.get("限制范围", "")).strip() if not pd.isna(row.get("限制范围")) else None,
            common_alias=str(row.get("常用别名", "")).strip() if not pd.isna(row.get("常用别名")) else None,
            has_qualification=has_qualification,
            qualification_date=(
                str(row.get("取得资质时间", "")).strip() if not pd.isna(row.get("取得资质时间")) else None
            ),
        )

    async def _check_existing_record(self, category: str, parameter: str, method: str) -> bool:
        """
        检查是否存在相同的记录

        :param category: 检测类别
        :param parameter: 检测参数
        :param method: 检测方法
        :return: 是否存在
        """
        stmt = select(TechnicalManual).where(
            TechnicalManual.category == category,
            TechnicalManual.parameter == parameter,
            TechnicalManual.method == method,
            TechnicalManual.del_flag == "0",
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none() is not None

    def _parse_date(self, date_str: Optional[str]) -> Optional[date]:
        """
        解析日期字符串，支持多种格式

        :param date_str: 日期字符串
        :return: 日期对象
        """
        if not date_str or date_str.strip() == "":
            return None

        date_str = date_str.strip()

        # 支持的日期格式
        date_formats = [
            "%Y/%m/%d",  # 2025/1/2
            "%Y-%m-%d",  # 2025-01-02
            "%Y/%m/%d",  # 2025/01/02
            "%Y-%m-%d",  # 2025-1-2
            "%Y年%m月%d日",  # 2025年1月2日
            "%Y.%m.%d",  # 2025.1.2
            "%Y.%m.%d",  # 2025.01.02
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt).date()
            except ValueError:
                continue

        # 如果都不匹配，尝试使用pandas的日期解析
        try:
            return pd.to_datetime(date_str).date()
        except:
            raise ValueError(f"日期格式无效：{date_str}，支持格式：2025/1/2, 2025-01-02等")

    async def generate_template(self) -> bytes:
        """
        生成Excel导入模板

        :return: Excel文件字节数据
        """
        # 创建示例数据
        template_data = {
            "分类": ["气", "水", "土壤"],
            "检测类别": ["环境空气和废气", "水和废水", "土壤和沉积物"],
            "检测参数": ["PM2.5", "pH值", "重金属"],
            "检测方法": ["重量法", "玻璃电极法", "原子吸收分光光度法"],
            "资质编号": ["ZZ001", "ZZ002", "ZZ003"],
            "限制范围": ["0-500μg/m³", "6.0-9.0", "0-100mg/kg"],
            "常用别名": ["细颗粒物", "酸碱度", "重金属含量"],
            "是否资质": ["有", "有", "无"],
            "取得资质时间": ["2025/1/2", "2025-01-03", "2025.1.4"],
        }

        df = pd.DataFrame(template_data)

        # 保存到字节流
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="技术手册导入模板", index=False)

        output.seek(0)
        return output.getvalue()
