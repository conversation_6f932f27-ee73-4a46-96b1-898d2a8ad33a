from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, DECIMAL
from config.database import Base


class PriceList(Base):
    """
    价目表
    """

    __tablename__ = 'price_list'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    price_code = Column(String(20), nullable=True, comment='报价编号')
    test_code = Column(String(20), nullable=False, comment='检测编号')
    sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='采样单价')
    testing_price = Column(DECIMAL(10, 2), nullable=False, comment='检测单价')
    travel_price = Column(DECIMAL(10, 2), nullable=True, comment='差旅费单价')
    effective_date = Column(DateTime, nullable=False, comment='生效日期')
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    del_flag = Column(String(1), default='0', comment='删除标志（0存在 2删除）')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now(), comment='更新时间')
    remark = Column(String(500), default=None, comment='备注')
