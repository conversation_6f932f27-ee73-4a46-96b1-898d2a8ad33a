"""
项目报价控制器
"""

from typing import Optional

from fastapi import APIRouter, Depends, Path, Query, Request, Response
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_customer.service.customer_service import CustomerService
from module_quotation.entity.vo.project_quotation_vo import (
    AddProjectQuotationModel,
    EditProjectQuotationModel,
    ProjectQuotationModel,
    ProjectQuotationPageQueryModel,
    ProjectQuotationQueryModel,
)
from module_quotation.service.project_quotation_service import ProjectQuotationService
from utils.response_util import ResponseUtil

router = APIRouter(
    prefix="/quotation/project-quotation",
    tags=["项目报价管理"],
)


@router.get("/page", summary="分页查询项目报价")
async def get_project_quotation_page(
    request: Request,
    query_params: ProjectQuotationPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    分页查询项目报价

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 分页查询结果
    """
    # 查询项目报价
    service = ProjectQuotationService(db)
    result = await service.get_project_quotation_page(query_params)

    return ResponseUtil.success(data=result)


@router.get("/list", summary="查询项目报价列表")
async def get_project_quotation_list(
    request: Request,
    query_params: ProjectQuotationPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    查询项目报价列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 项目报价列表
    """
    # 查询项目报价
    service = ProjectQuotationService(db)
    result = await service.get_project_quotation_list(query_params)

    return ResponseUtil.success(data=result)


@router.get("/{id}", response_model=ProjectQuotationModel, summary="获取项目报价详情")
async def get_project_quotation(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取项目报价详情

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 项目报价详情
    """
    service = ProjectQuotationService(db)
    result = await service.get_project_quotation(id)

    return ResponseUtil.success(data=result)


@router.post("", response_model=CrudResponseModel, summary="添加项目报价")
async def add_project_quotation(
    request: Request,
    project_quotation: AddProjectQuotationModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    添加项目报价

    :param request: 请求对象
    :param project_quotation: 项目报价信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 添加结果
    """
    service = ProjectQuotationService(db)
    result = await service.add_project_quotation(project_quotation, current_user)

    return ResponseUtil.success(data=result)


@router.put("", response_model=CrudResponseModel, summary="编辑项目报价")
async def edit_project_quotation(
    request: Request,
    project_quotation: EditProjectQuotationModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑项目报价

    :param request: 请求对象
    :param project_quotation: 项目报价信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = ProjectQuotationService(db)
    result = await service.edit_project_quotation(project_quotation, current_user)

    return ResponseUtil.success(data=result)


@router.put("/quotation-fee", response_model=CrudResponseModel, summary="更新项目报价费用")
async def update_project_quotation_fee(
    request: Request,
    project_quotation: dict,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新项目报价费用

    :param request: 请求对象
    :param project_quotation: 项目报价信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 更新结果
    """
    service = ProjectQuotationService(db)
    result = await service.update_project_quotation_fee(project_quotation, current_user)

    return ResponseUtil.success(data=result)


@router.put("/other-fee", response_model=CrudResponseModel, summary="更新单行其他费用")
async def update_single_other_fee(
    request: Request,
    data: dict,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    更新单行其他费用

    :param request: 请求对象
    :param data: 其他费用信息
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 更新结果
    """
    service = ProjectQuotationService(db)
    result = await service.update_single_other_fee(data, current_user)

    return ResponseUtil.success(data=result)


@router.delete("/{id}", response_model=CrudResponseModel, summary="删除项目报价")
async def delete_project_quotation(
    request: Request,
    id: int = Path(..., description="项目报价ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除项目报价

    :param request: 请求对象
    :param id: 项目报价ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = ProjectQuotationService(db)
    result = await service.delete_project_quotation(id, current_user)

    return ResponseUtil.success(data=result)


@router.get("/export", summary="导出项目报价")
async def export_project_quotation(
    request: Request,
    export_type: str = Query(..., description="导出类型（excel, pdf, word）"),
    project_name: Optional[str] = Query(None, description="项目名称"),
    project_code: Optional[str] = Query(None, description="项目编号"),
    customer_name: Optional[str] = Query(None, description="客户名称"),
    status: Optional[str] = Query(None, description="项目审批状态"),
    begin_time: Optional[str] = Query(None, description="开始时间"),
    end_time: Optional[str] = Query(None, description="结束时间"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
) -> Response:
    """
    导出项目报价

    :param request: 请求对象
    :param export_type: 导出类型（excel, pdf, word）
    :param project_name: 项目名称
    :param project_code: 项目编号
    :param customer_name: 客户名称
    :param status: 项目审批状态
    :param begin_time: 开始时间
    :param end_time: 结束时间
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件响应
    """
    # 构建查询参数
    query_params = ProjectQuotationQueryModel(
        project_name=project_name,
        project_code=project_code,
        customer_name=customer_name,
        status=status,
        begin_time=begin_time,
        end_time=end_time,
    )

    # 导出项目报价
    service = ProjectQuotationService(db)
    return await service.export_project_quotation(query_params, export_type)


@router.get("/customer/search", summary="搜索客户")
async def search_customers(
    request: Request,
    keyword: str = Query(..., description="关键字"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    搜索客户

    :param request: 请求对象
    :param keyword: 关键字
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 客户列表
    """
    result = await CustomerService.search_customers_by_name(db, keyword)
    return ResponseUtil.success(data=result)
