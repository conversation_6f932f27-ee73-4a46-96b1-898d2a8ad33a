from fastapi import APIRouter, Depends, Request, Path, Query, Response
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.get_db import get_db
from utils.response_util import ResponseUtil
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_basedata.entity.vo.technical_manual_vo import (
    AddTechnicalManualModel,
    BatchInputTechnicalManualModel,
    BatchUpdateTechnicalManualModel,
    EditTechnicalManualModel,
    TechnicalManualModel,
    TechnicalManualPageQueryModel,
    TechnicalManualQueryModel,
)
from module_basedata.service.technical_manual_service import TechnicalManualService
from utils.page_util import PageResponseModel

# 创建路由
router = APIRouter(prefix='/basedata/technical-manual', tags=['技术手册管理'])


@router.get('/list', response_model=List[TechnicalManualModel], summary='获取技术手册列表')
async def get_technical_manual_list(
    request: Request,
    query_params: TechnicalManualQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册列表
    """
    service = TechnicalManualService(db)
    technical_manual_list = await service.get_technical_manual_list(query_params)
    return ResponseUtil.success(data=technical_manual_list)


@router.get('/page', response_model=PageResponseModel, summary='获取技术手册分页列表')
async def get_technical_manual_page(
    request: Request,
    query_params: TechnicalManualPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册分页列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册分页列表
    """
    service = TechnicalManualService(db)
    technical_manual_page = await service.get_technical_manual_page(query_params)
    return ResponseUtil.success(data=technical_manual_page)


@router.get('/category-tree', summary='获取检测类别树形数据')
async def get_category_tree(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取检测类别树形数据

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测类别树形数据
    """
    service = TechnicalManualService(db)
    category_tree = await service.get_category_tree()
    return ResponseUtil.success(data=category_tree)


@router.get('/{id}', response_model=TechnicalManualModel, summary='获取技术手册详情')
async def get_technical_manual_detail(
    request: Request,
    id: int = Path(..., description='技术手册ID'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册详情

    :param request: 请求对象
    :param id: 技术手册ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册详情
    """
    service = TechnicalManualService(db)
    technical_manual = await service.get_technical_manual_detail(id)
    return ResponseUtil.success(data=technical_manual)


@router.post('', response_model=CrudResponseModel, summary='新增技术手册')
async def add_technical_manual(
    request: Request,
    technical_manual: AddTechnicalManualModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增技术手册

    :param request: 请求对象
    :param technical_manual: 新增技术手册对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = TechnicalManualService(db)
    result = await service.add_technical_manual(request, technical_manual, current_user)
    return ResponseUtil.success(data=result)


@router.put('', response_model=CrudResponseModel, summary='编辑技术手册')
async def edit_technical_manual(
    request: Request,
    technical_manual: EditTechnicalManualModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑技术手册

    :param request: 请求对象
    :param technical_manual: 编辑技术手册对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = TechnicalManualService(db)
    result = await service.edit_technical_manual(request, technical_manual, current_user)
    return ResponseUtil.success(data=result)


@router.delete('/{id}', response_model=CrudResponseModel, summary='删除技术手册')
async def delete_technical_manual(
    request: Request,
    id: int = Path(..., description='技术手册ID'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除技术手册

    :param request: 请求对象
    :param id: 技术手册ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = TechnicalManualService(db)
    result = await service.delete_technical_manual(id, current_user)
    return ResponseUtil.success(data=result)


@router.get('/options/test-codes', summary='获取所有检测编号')
async def get_test_codes(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取所有检测编号

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测编号列表
    """
    service = TechnicalManualService(db)
    test_codes = await service.get_test_codes()
    return ResponseUtil.success(data=test_codes)


@router.get('/by-test-code/{test_code}', response_model=TechnicalManualModel, summary='根据检测编号获取技术手册详情')
async def get_technical_manual_by_test_code(
    request: Request,
    test_code: str = Path(..., description='检测编号'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    根据检测编号获取技术手册详情

    :param request: 请求对象
    :param test_code: 检测编号
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册详情
    """
    service = TechnicalManualService(db)
    technical_manual = await service.get_technical_manual_by_test_code(test_code)
    return ResponseUtil.success(data=technical_manual)


@router.get('/by-params/', response_model=TechnicalManualModel, summary='根据检测类别、参数和方法获取技术手册详情')
async def get_technical_manual_by_params(
    request: Request,
    category: str = Query(..., description='检测类别'),
    parameter: str = Query(..., description='检测参数'),
    method: str = Query(..., description='检测方法'),
    db: AsyncSession = Depends(get_db),
):
    """
    根据检测类别、参数和方法获取技术手册详情

    :param request: 请求对象
    :param category: 检测类别
    :param parameter: 检测参数
    :param method: 检测方法
    :param db: 数据库会话
    :return: 技术手册详情
    """
    try:
        # 调用服务层方法
        service = TechnicalManualService(db)
        technical_manual = await service.get_technical_manual_by_params(category, parameter, method)

        # 返回结果
        return ResponseUtil.success(data=technical_manual)
    except Exception as e:
        # 记录错误并返回友好的错误信息
        import traceback
        traceback.print_exc()
        return ResponseUtil.error(msg=f'获取技术手册详情失败: {str(e)}')


@router.get('/options/categories', summary='获取所有检测类别')
async def get_categories(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取所有检测类别

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测类别列表
    """
    service = TechnicalManualService(db)
    categories = await service.get_categories()
    return ResponseUtil.success(data=categories)


@router.get('/options/parameters', summary='获取检测参数列表')
async def get_parameters(
    request: Request,
    category: Optional[str] = Query(None, description='检测类别'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取检测参数列表

    :param request: 请求对象
    :param category: 检测类别
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测参数列表
    """
    service = TechnicalManualService(db)
    parameters = await service.get_parameters(category)
    return ResponseUtil.success(data=parameters)


@router.get('/options/methods', summary='获取检测方法列表')
async def get_methods(
    request: Request,
    category: Optional[str] = Query(None, description='检测类别'),
    parameter: Optional[str] = Query(None, description='检测参数'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取检测方法列表

    :param request: 请求对象
    :param category: 检测类别
    :param parameter: 检测参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测方法列表
    """
    service = TechnicalManualService(db)
    methods = await service.get_methods(category, parameter)
    return ResponseUtil.success(data=methods)


@router.post('/batch-input', response_model=CrudResponseModel, summary='批量录入技术手册')
async def batch_input_technical_manual(
    request: Request,
    batch_input_model: BatchInputTechnicalManualModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量录入技术手册

    :param request: 请求对象
    :param batch_input_model: 批量录入技术手册对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 批量录入结果
    """
    service = TechnicalManualService(db)
    result = await service.batch_input_technical_manual(request, batch_input_model, current_user)
    return ResponseUtil.success(data=result)


@router.post('/batch-update', response_model=CrudResponseModel, summary='批量更新技术手册')
async def batch_update_technical_manual(
    request: Request,
    batch_update_model: BatchUpdateTechnicalManualModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    批量更新技术手册

    :param request: 请求对象
    :param batch_update_model: 批量更新技术手册对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 批量更新结果
    """
    service = TechnicalManualService(db)
    result = await service.batch_update_technical_manual(request, batch_update_model, current_user)
    return ResponseUtil.success(data=result)


@router.get('/export', summary='导出技术手册')
async def export_technical_manual(
    request: Request,
    export_type: str = Query(..., description='导出类型（excel, pdf, word）'),
    category: Optional[str] = Query(None, description='检测类别'),
    parameter: Optional[str] = Query(None, description='检测参数'),
    method: Optional[str] = Query(None, description='检测方法'),
    keyword: Optional[str] = Query(None, description='关键字'),
    begin_time: Optional[str] = Query(None, description='开始时间'),
    end_time: Optional[str] = Query(None, description='结束时间'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
) -> Response:
    """
    导出技术手册

    :param request: 请求对象
    :param export_type: 导出类型（excel, pdf, word）
    :param category: 检测类别
    :param parameter: 检测参数
    :param method: 检测方法
    :param keyword: 关键字
    :param begin_time: 开始时间
    :param end_time: 结束时间
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件响应
    """
    # 构建查询参数
    query_params = TechnicalManualQueryModel(
        category=category,
        parameter=parameter,
        method=method,
        keyword=keyword,
        begin_time=begin_time,
        end_time=end_time
    )

    # 导出技术手册
    service = TechnicalManualService(db)
    return await service.export_technical_manual(query_params, export_type)
