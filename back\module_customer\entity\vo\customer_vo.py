from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, model_validator
from pydantic.alias_generators import to_camel
from pydantic_validation_decorator import Network, NotBlank, Size
from typing import List, Literal, Optional, Union
from module_admin.annotation.pydantic_annotation import as_query
from module_admin.entity.vo.user_vo import UserModel


class CustomerContactModel(BaseModel):
    """
    客户联系人模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    contact_id: Optional[int] = Field(default=None, description='联系人ID')
    customer_id: Optional[int] = Field(default=None, description='客户ID')
    contact_name: Optional[str] = Field(default=None, description='联系人姓名')
    position: Optional[str] = Field(default=None, description='职务')
    phone: Optional[str] = Field(default=None, description='电话')
    wechat: Optional[str] = Field(default=None, description='微信')
    email: Optional[str] = Field(default=None, description='邮箱')
    is_primary: Optional[Literal['0', '1']] = Field(default='0', description='是否主要联系人（0否 1是）')
    status: Optional[Literal['0', '1']] = Field(default='0', description='状态（0正常 1停用）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')

    @NotBlank(field_name='contact_name', message='联系人姓名不能为空')
    @Size(field_name='contact_name', min_length=0, max_length=50, message='联系人姓名长度不能超过50个字符')
    def get_contact_name(self):
        return self.contact_name

    @Size(field_name='phone', min_length=0, max_length=20, message='电话长度不能超过20个字符')
    def get_phone(self):
        return self.phone

    @Network(field_name='email', field_type='EmailStr', message='邮箱格式不正确')
    @Size(field_name='email', min_length=0, max_length=100, message='邮箱长度不能超过100个字符')
    def get_email(self):
        return self.email


class CustomerInternalManagerModel(BaseModel):
    """
    客户内部负责人模型
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description='关联ID')
    customer_id: Optional[int] = Field(default=None, description='客户ID')
    user_id: Optional[int] = Field(default=None, description='内部负责人ID')
    is_primary: Optional[Literal['0', '1']] = Field(default='0', description='是否主要负责人（0否 1是）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    user: Optional[UserModel] = Field(default=None, description='用户信息')


class CustomerModel(BaseModel):
    """
    客户模型（包含集团和公司）
    """
    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    customer_id: Optional[int] = Field(default=None, description='客户ID')
    customer_name: Optional[str] = Field(default=None, description='客户名称')
    customer_level: Optional[Literal['1', '2']] = Field(default=None, description='客户级别（1集团 2公司）')
    parent_id: Optional[int] = Field(default=None, description='上级客户ID')
    customer_type: Optional[str] = Field(default=None, description='客户类型（政府/企业/园区）')
    customer_source: Optional[str] = Field(default=None, description='客户来源')
    province: Optional[str] = Field(default=None, description='省份')
    city: Optional[str] = Field(default=None, description='城市')
    district: Optional[str] = Field(default=None, description='区县')
    address: Optional[str] = Field(default=None, description='详细地址')
    status: Optional[Literal['0', '1']] = Field(default='0', description='状态（0正常 1停用）')
    del_flag: Optional[Literal['0', '2']] = Field(default='0', description='删除标志（0存在 2删除）')
    create_by: Optional[str] = Field(default=None, description='创建者')
    create_time: Optional[datetime] = Field(default=None, description='创建时间')
    update_by: Optional[str] = Field(default=None, description='更新者')
    update_time: Optional[datetime] = Field(default=None, description='更新时间')
    remark: Optional[str] = Field(default=None, description='备注')
    parent: Optional['CustomerModel'] = Field(default=None, description='上级客户信息')
    children: Optional[List['CustomerModel']] = Field(default=[], description='下级客户列表')
    contacts: Optional[List[CustomerContactModel]] = Field(default=[], description='联系人列表')
    internal_managers: Optional[List[CustomerInternalManagerModel]] = Field(default=[], description='内部负责人列表')

    @NotBlank(field_name='customer_name', message='客户名称不能为空')
    @Size(field_name='customer_name', min_length=0, max_length=100, message='客户名称长度不能超过100个字符')
    def get_customer_name(self):
        return self.customer_name

    @NotBlank(field_name='customer_level', message='客户级别不能为空')
    def get_customer_level(self):
        return self.customer_level

    @NotBlank(field_name='customer_type', message='客户类型不能为空')
    def get_customer_type(self):
        return self.customer_type

    @NotBlank(field_name='customer_source', message='客户来源不能为空')
    def get_customer_source(self):
        return self.customer_source

    @NotBlank(field_name='province', message='省份不能为空')
    def get_province(self):
        return self.province

    @NotBlank(field_name='city', message='城市不能为空')
    def get_city(self):
        return self.city

    @model_validator(mode='after')
    def validate_parent_id(self) -> 'CustomerModel':
        if self.customer_level == '2' and not self.parent_id:
            raise ValueError('公司级客户必须关联上级集团')
        return self


@as_query
class CustomerQueryModel(CustomerModel):
    """
    客户查询模型
    """
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class CustomerPageQueryModel(CustomerQueryModel):
    """
    客户分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class AddCustomerModel(CustomerModel):
    """
    新增客户模型
    """
    contacts: List[CustomerContactModel] = Field(default=[], description='联系人列表')
    internal_managers: List[CustomerInternalManagerModel] = Field(default=[], description='内部负责人列表')


class EditCustomerModel(AddCustomerModel):
    """
    编辑客户模型
    """
    pass
