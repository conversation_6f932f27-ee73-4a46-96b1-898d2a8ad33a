import request from '@/utils/request'
import { getUsersByRoleName } from '@/api/system/role'
import { listUser } from '@/api/system/user'

/**
 * 获取审核员选项列表
 * @param {string} roleName - 角色名称，默认为'审核员'
 * @returns {Promise} 返回格式化的审核员选项列表
 */
export function getApproverOptions(roleName = '审核员') {
  return new Promise((resolve, reject) => {
    // 从角色中获取审核人列表
    getUsersByRoleName(roleName).then(response => {
      const approverOptions = response.data.map(user => ({
        value: user.userId,
        label: `${user.userName}(${user.nickName || '无昵称'})`
      }))
      resolve(approverOptions)
    }).catch(error => {
      console.error('获取审核人列表失败:', error)
      // 如果获取审核人角色失败，则获取所有用户作为备选
      listUser().then(response => {
        const approverOptions = response.data.rows.map(user => ({
          value: user.userId,
          label: `${user.userName}(${user.nickName || '无昵称'})`
        }))
        resolve(approverOptions)
      }).catch(err => {
        console.error('获取用户列表失败:', err)
        reject(err)
      })
    })
  })
}

/**
 * 获取指定角色的用户列表（原始数据格式）
 * @param {string} roleName - 角色名称
 * @returns {Promise} 返回用户列表原始数据
 */
export function getUsersByRole(roleName) {
  return getUsersByRoleName(roleName)
}

/**
 * 获取所有用户列表
 * @param {Object} query - 查询参数
 * @returns {Promise} 返回用户列表
 */
export function getAllUsers(query = {}) {
  return listUser(query)
}