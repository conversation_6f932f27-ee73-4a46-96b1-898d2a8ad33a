from datetime import datetime

from fastapi import Request, Response
from sqlalchemy import and_, or_, select, distinct, func
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.price_list_do import PriceList
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.price_list_vo import (
    AddPriceListModel,
    EditPriceListModel,
    PriceListPageQueryModel,
    PriceListQueryModel,
)
from utils.export_util import ExportUtil
from utils.common_util import CamelCaseUtil


class PriceListService(BaseService[PriceList]):
    """
    价目表管理模块服务层
    """

    async def generate_price_code(self):
        """
        生成报价编号

        :return: 报价编号
        """
        # 查询最大的报价编号
        stmt = select(func.max(PriceList.price_code)).where(
            PriceList.price_code.like('JG%'),
            PriceList.del_flag == '0'
        )
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        # 如果没有报价编号，则从JG100001开始
        if not max_code:
            return 'JG100001'

        # 提取数字部分并加1
        try:
            num = int(max_code[2:]) + 1
            return f'JG{num:06d}'
        except (ValueError, IndexError):
            # 如果解析失败，则从JG100001开始
            return 'JG100001'

    def __init__(self, db: AsyncSession):
        """
        初始化价目表服务

        :param db: 数据库会话
        """
        super().__init__(PriceList, db)
        self.db = db

    async def get_price_list_list(self, query_object: PriceListQueryModel):
        """
        获取价目表列表

        :param query_object: 查询参数对象
        :return: 价目表列表
        """
        # 构建查询条件
        conditions = [PriceList.del_flag == '0']

        if query_object.test_code:
            conditions.append(PriceList.test_code == query_object.test_code)

        # 如果有检测类别、参数或方法条件，需要关联技术手册表查询
        if query_object.category or query_object.parameter or query_object.method:
            # 获取符合条件的检测编号
            tech_conditions = [TechnicalManual.del_flag == '0']
            if query_object.category:
                tech_conditions.append(TechnicalManual.category == query_object.category)
            if query_object.parameter:
                tech_conditions.append(TechnicalManual.parameter == query_object.parameter)
            if query_object.method:
                tech_conditions.append(TechnicalManual.method == query_object.method)

            stmt = select(TechnicalManual.test_code).where(and_(*tech_conditions))
            result = await self.db.execute(stmt)
            test_codes = result.scalars().all()

            if test_codes:
                conditions.append(PriceList.test_code.in_(test_codes))
            else:
                # 如果没有符合条件的检测编号，则返回空列表
                return []
        if query_object.keyword:
            keyword_condition = or_(
                PriceList.test_code.like(f'%{query_object.keyword}%'),
                PriceList.price_code.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                PriceList.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                PriceList.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(PriceList).where(and_(*conditions)).order_by(
            PriceList.test_code, PriceList.effective_date.desc()
        )
        result = await self.db.execute(stmt)
        price_list = result.scalars().all()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(price_list)

    async def get_price_list_page(self, query_object: PriceListPageQueryModel):
        """
        获取价目表分页列表

        :param query_object: 查询参数对象
        :return: 价目表分页列表
        """
        from utils.page_util import PageUtil

        # 构建查询条件
        conditions = [PriceList.del_flag == '0']

        if query_object.test_code:
            conditions.append(PriceList.test_code == query_object.test_code)

        # 如果有检测类别、参数或方法条件，需要关联技术手册表查询
        if query_object.category or query_object.parameter or query_object.method:
            # 获取符合条件的检测编号
            tech_conditions = [TechnicalManual.del_flag == '0']
            if query_object.category:
                tech_conditions.append(TechnicalManual.category == query_object.category)
            if query_object.parameter:
                tech_conditions.append(TechnicalManual.parameter == query_object.parameter)
            if query_object.method:
                tech_conditions.append(TechnicalManual.method == query_object.method)

            stmt = select(TechnicalManual.test_code).where(and_(*tech_conditions))
            result = await self.db.execute(stmt)
            test_codes = result.scalars().all()

            if test_codes:
                conditions.append(PriceList.test_code.in_(test_codes))
            else:
                # 如果没有符合条件的检测编号，则返回空列表
                return {"total": 0, "rows": []}
        if query_object.keyword:
            keyword_condition = or_(
                PriceList.test_code.like(f'%{query_object.keyword}%'),
                PriceList.price_code.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                PriceList.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                PriceList.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(PriceList).where(and_(*conditions)).order_by(
            PriceList.test_code, PriceList.effective_date.desc()
        )
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_price_list_detail(self, id: int):
        """
        获取价目表详情

        :param id: 价目表ID
        :return: 价目表详情
        """
        stmt = select(PriceList).where(PriceList.id == id)
        result = await self.db.execute(stmt)
        price_list = result.scalars().first()
        if not price_list:
            raise ServiceException(message=f'价目表ID：{id}不存在')

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(price_list)

    async def get_price_list_by_test_code(self, test_code: str):
        """
        根据检测编号获取价目表

        :param test_code: 检测编号
        :return: 价目表详情
        """
        # 查询最新的有效价目表
        stmt = select(PriceList).where(
            PriceList.test_code == test_code,
            PriceList.status == '0',  # 启用状态
            PriceList.del_flag == '0'
        ).order_by(PriceList.effective_date.desc())

        result = await self.db.execute(stmt)
        price_list = result.scalars().first()

        if not price_list:
            raise ServiceException(message=f'检测编号：{test_code}的价目表不存在或未启用')

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(price_list)

    async def check_price_list_unique(self, test_code: str, effective_date: str, id: int = None):
        """
        检查价目表是否唯一

        :param test_code: 检测编号
        :param effective_date: 生效日期
        :param id: 价目表ID
        :return: 是否唯一
        """
        conditions = [
            PriceList.test_code == test_code,
            PriceList.effective_date == effective_date,
            PriceList.del_flag == '0'
        ]

        if id:
            conditions.append(PriceList.id != id)

        stmt = select(PriceList).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def add_price_list(self, _: Request, price_list_model: AddPriceListModel, current_user: CurrentUserModel):
        """
        新增价目表

        :param request: 请求对象
        :param price_list_model: 新增价目表对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 新增结果
        """
        try:
            # 校验价目表是否唯一
            if not await self.check_price_list_unique(
                price_list_model.test_code,
                price_list_model.effective_date
            ):
                raise ServiceException(message=f'新增价目表失败，该检测编号和生效日期组合已存在')

            # 生成报价编号
            price_code = await self.generate_price_code()

            # 创建价目表对象
            price_list = PriceList(
                price_code=price_code,
                test_code=price_list_model.test_code,
                sampling_price=price_list_model.sampling_price,
                testing_price=price_list_model.testing_price,
                travel_price=price_list_model.travel_price,
                effective_date=price_list_model.effective_date,
                status=price_list_model.status,
                create_by=current_user.user.user_name if current_user and current_user.user else '',
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else '',
                update_time=datetime.now(),
                remark=price_list_model.remark
            )

            # 新增价目表
            self.db.add(price_list)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='新增成功',
                result={'id': price_list.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("添加价目表失败", e)
            raise ServiceException(message=f'新增价目表失败: {str(e)}')

    async def edit_price_list(self, _: Request, price_list_model: EditPriceListModel, current_user: CurrentUserModel):
        """
        编辑价目表

        :param request: 请求对象
        :param price_list_model: 编辑价目表对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 编辑结果
        """
        try:
            # 校验价目表是否存在
            stmt = select(PriceList).where(PriceList.id == price_list_model.id)
            result = await self.db.execute(stmt)
            price_list = result.scalars().first()
            if not price_list:
                raise ServiceException(message=f'价目表ID：{price_list_model.id}不存在')

            # 校验价目表是否唯一
            if not await self.check_price_list_unique(
                price_list_model.test_code,
                price_list_model.effective_date,
                price_list_model.id
            ):
                raise ServiceException(message=f'修改价目表失败，该检测编号和生效日期组合已存在')

            # 更新价目表对象
            price_list.test_code = price_list_model.test_code
            price_list.sampling_price = price_list_model.sampling_price
            price_list.testing_price = price_list_model.testing_price
            price_list.travel_price = price_list_model.travel_price
            price_list.effective_date = price_list_model.effective_date
            price_list.status = price_list_model.status
            price_list.update_by = current_user.user.user_name if current_user and current_user.user else ''
            price_list.update_time = datetime.now()
            price_list.remark = price_list_model.remark

            # 更新价目表
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='修改成功',
                result={'id': price_list.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("修改价目表失败", e)
            raise ServiceException(message=f'修改价目表失败: {str(e)}')

    async def delete_price_list(self, id: int, current_user: CurrentUserModel):
        """
        删除价目表

        :param id: 价目表ID
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 删除结果
        """
        try:
            # 校验价目表是否存在
            stmt = select(PriceList).where(PriceList.id == id)
            result = await self.db.execute(stmt)
            price_list = result.scalars().first()
            if not price_list:
                raise ServiceException(message=f'价目表ID：{id}不存在')

            # 软删除价目表
            price_list.del_flag = '2'
            price_list.update_by = current_user.user.user_name if current_user and current_user.user else ''
            price_list.update_time = datetime.now()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='删除成功',
                result={'id': id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("删除价目表失败", e)
            raise ServiceException(message=f'删除价目表失败: {str(e)}')

    async def get_parameters(self):
        """
        获取所有检测参数

        :return: 检测参数列表
        """
        # 从技术手册表中获取参数
        stmt = select(distinct(TechnicalManual.parameter)).where(TechnicalManual.del_flag == '0')
        result = await self.db.execute(stmt)
        parameters = result.scalars().all()
        parameter_list = [{'parameter': parameter} for parameter in parameters]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(parameter_list)

    async def get_methods(self, parameter: str = None):
        """
        获取检测方法列表

        :param parameter: 检测参数
        :return: 检测方法列表
        """
        # 从技术手册表中获取方法
        conditions = [TechnicalManual.del_flag == '0']
        if parameter:
            conditions.append(TechnicalManual.parameter == parameter)

        stmt = select(distinct(TechnicalManual.method)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        methods = result.scalars().all()
        method_list = [{'method': method} for method in methods]

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(method_list)

    async def export_price_list(self, query_object: PriceListQueryModel, export_type: str) -> Response:
        """
        导出价目表

        :param query_object: 查询参数对象
        :param export_type: 导出类型（excel, pdf, word）
        :return: 导出文件响应
        """
        # 获取价目表列表
        price_lists = await self.get_price_list_list(query_object)

        # 转换为字典列表
        data = []
        for price in price_lists:
            # 获取技术手册信息
            stmt = select(TechnicalManual).where(
                TechnicalManual.test_code == price.test_code,
                TechnicalManual.del_flag == '0'
            )
            result = await self.db.execute(stmt)
            technical_manual = result.scalars().first()

            # 将SQLAlchemy模型转换为字典
            price_dict = {
                'id': price.id,
                'price_code': price.price_code or '',
                'test_code': price.test_code or '',
                'category': technical_manual.category if technical_manual else '',
                'parameter': technical_manual.parameter if technical_manual else '',
                'method': technical_manual.method if technical_manual else '',
                'sampling_price': price.sampling_price,
                'testing_price': price.testing_price,
                'travel_price': price.travel_price,
                'effective_date': price.effective_date,
                'status': '启用' if price.status == '0' else '停用',
                'create_time': price.create_time.strftime('%Y-%m-%d %H:%M:%S') if price.create_time else '',
                'create_by': price.create_by,
                'remark': price.remark or ''
            }
            data.append(price_dict)

        # 定义表头映射
        headers = {
            'id': 'ID',
            'price_code': '报价编号',
            'test_code': '检测编号',
            'category': '检测类别',
            'parameter': '检测参数',
            'method': '检测方法',
            'sampling_price': '采样费用',
            'testing_price': '检测费用',
            'travel_price': '差旅费用',
            'effective_date': '生效日期',
            'status': '状态',
            'create_time': '创建时间',
            'create_by': '创建人',
            'remark': '备注'
        }

        # 将数据转换为驼峰命名
        camel_data = CamelCaseUtil.transform_result(data)
        camel_headers = CamelCaseUtil.transform_result(headers)

        # 根据导出类型导出文件
        if export_type.lower() == 'excel':
            return await ExportUtil.export_excel(camel_data, '价目表', camel_headers)
        elif export_type.lower() == 'pdf':
            return await ExportUtil.export_pdf(camel_data, '价目表', camel_headers, '价目表列表')
        elif export_type.lower() == 'word':
            return await ExportUtil.export_word(camel_data, '价目表', camel_headers, '价目表列表')
        else:
            raise ServiceException(message=f'不支持的导出类型: {export_type}')
