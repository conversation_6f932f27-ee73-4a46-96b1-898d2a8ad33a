<template>
  <!-- 新增项目报价弹框 -->
  <el-dialog title="新增项目报价" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-steps :active="active" finish-status="success" simple style="margin-bottom: 20px">
      <el-step title="基本信息" />
      <el-step title="检测项目" />
      <el-step title="确认提交" />
    </el-steps>

    <!-- 基本信息表单 -->
    <div v-if="active === 0">
      <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="合同编号" prop="contractCode">
          <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
        </el-form-item>

        <el-divider content-position="left">客户信息</el-divider>
        <el-form-item label="客户名称" prop="customerName">
          <el-autocomplete
            v-model="form.customerName"
            :fetch-suggestions="queryCustomers"
            placeholder="请输入客户名称"
            @select="handleCustomerSelect"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="客户地址">
          <el-input v-model="form.customerAddress" placeholder="客户地址" disabled />
        </el-form-item>
        <el-form-item label="客户联系人">
          <el-input v-model="form.customerContact" placeholder="客户联系人" disabled />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="form.customerPhone" placeholder="联系电话" disabled />
        </el-form-item>
        <el-divider content-position="left">受检方信息</el-divider>
        <el-form-item label="受检方企业名称" prop="inspectedParty">
          <el-input v-model="form.inspectedParty" placeholder="请输入受检方企业名称" />
        </el-form-item>
        <el-form-item label="受检方联系人" prop="inspectedContact">
          <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
        </el-form-item>
        <el-form-item label="受检方联系电话" prop="inspectedPhone">
          <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
        </el-form-item>
        <el-form-item label="受检方详细地址" prop="inspectedAddress">
          <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
        </el-form-item>
        <el-divider content-position="left">项目信息</el-divider>
        <el-form-item label="项目负责人" prop="projectManager">
          <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
        </el-form-item>
        <el-form-item label="市场负责人" prop="marketManager">
          <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
        </el-form-item>
        <el-form-item label="项目技术人" prop="technicalManager">
          <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
        </el-form-item>
        <el-form-item label="委托日期" prop="commissionDate">
          <el-date-picker
            v-model="form.commissionDate"
            type="date"
            placeholder="选择委托日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="项目审批人" prop="approvers">
          <el-select
            v-model="form.approvers"
            multiple
            placeholder="请选择项目审批人"
            style="width: 100%"
          >
            <el-option
              v-for="item in approverOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
    </div>

    <!-- 检测明细项目配置 -->
    <div v-if="active === 1">
      <div style="margin-bottom: 20px">
        <el-button type="primary" @click="openQuotationDetail">配置检测明细项目</el-button>
      </div>

      <!-- 检测明细项目表格 -->
      <div v-if="quotationDetailData.length > 0">
        <el-table :data="quotationDetailData" style="width: 100%" border>
          <!-- 检测项目信息 -->
          <el-table-column label="检测项目" align="center">
            <el-table-column label="分类" prop="classification" width="100" show-overflow-tooltip />
            <el-table-column label="二级分类" prop="category" width="120" show-overflow-tooltip />
            <el-table-column label="指标" prop="parameter" width="150" show-overflow-tooltip fixed="left" />
            <el-table-column label="方法" prop="method" width="200" show-overflow-tooltip />
          </el-table-column>

          <!-- 采样信息 -->
          <el-table-column label="采样信息" align="center">
            <el-table-column label="样品来源" prop="sampleSource" width="100" />
            <el-table-column label="点位名称" prop="pointName" width="120">
              <template #default="{ row }">
                <el-tooltip :content="row.pointName" placement="top">
                  <span>{{ row.pointName }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="点位数" prop="pointCount" width="80">
              <template #default="{ row }">
                <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                  <span>{{ row.pointCount }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="检测周期类型" prop="cycleType" width="120" />
            <el-table-column label="检测周期数" prop="cycleCount" width="100" />
            <el-table-column label="检测频次数" prop="frequency" width="100">
              <template #default="{ row }">
                <el-tooltip content="检测频次(次/检测周期)" placement="top">
                  <span>{{ row.frequency }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="样品数" prop="sampleCount" width="80">
              <template #default="{ row }">
                <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                  <span>{{ row.sampleCount }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="是否分包" prop="isSubcontract" width="100">
              <template #default="{ row }">
                <el-tag :type="row.isSubcontract ? 'warning' : 'success'">
                  {{ row.isSubcontract ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table-column>

          <!-- 其他信息 -->
          <el-table-column label="其他信息" align="center">
            <el-table-column label="备注" prop="remark" width="150" show-overflow-tooltip />
          </el-table-column>
        </el-table>

        <div class="fee-summary">
          <span>检测明细项目数：{{ quotationDetailData.length }}</span>
        </div>
      </div>

      <div v-else class="empty-state">
        <el-empty description="请配置检测明细项目" />
      </div>

      <!-- 检测明细项目配置组件 -->
      <QuotationDetail
        :visible="quotationDetailOpen"
        :initial-data="quotationDetailState"
        @update:visible="quotationDetailOpen = $event"
        @confirm="handleQuotationDetailConfirm"
      />
    </div>

    <!-- 确认提交 -->
    <div v-if="active === 2">
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="项目名称">{{ form.projectName }}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{ form.contractCode }}</el-descriptions-item>
        <el-descriptions-item label="服务类型">{{ form.serviceType }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ form.customerName }}</el-descriptions-item>
        <el-descriptions-item label="受检方企业名称">{{ form.inspectedParty }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{ form.projectManager }}</el-descriptions-item>
        <el-descriptions-item label="市场负责人">{{ form.marketManager }}</el-descriptions-item>
        <el-descriptions-item label="项目技术人">{{ form.technicalManager }}</el-descriptions-item>
        <el-descriptions-item label="委托日期">{{ form.commissionDate }}</el-descriptions-item>
        <el-descriptions-item label="项目审批人">{{ form.approvers }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
      </el-descriptions>

      <div v-if="quotationDetailData.length > 0">
        <el-table :data="quotationDetailData" style="width: 100%" border>
          <!-- 检测项目信息 -->
          <el-table-column label="检测项目" align="center">
            <el-table-column label="分类" prop="classification" width="100" show-overflow-tooltip />
            <el-table-column label="二级分类" prop="category" width="120" show-overflow-tooltip />
            <el-table-column label="指标" prop="parameter" width="150" show-overflow-tooltip fixed="left" />
            <el-table-column label="方法" prop="method" width="200" show-overflow-tooltip />
          </el-table-column>

          <!-- 采样信息 -->
          <el-table-column label="采样信息" align="center">
            <el-table-column label="样品来源" prop="sampleSource" width="100" />
            <el-table-column label="点位名称" prop="pointName" width="120">
              <template #default="{ row }">
                <el-tooltip :content="row.pointName" placement="top">
                  <span>{{ row.pointName }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="点位数" prop="pointCount" width="80">
              <template #default="{ row }">
                <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                  <span>{{ row.pointCount }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="检测周期类型" prop="cycleType" width="120" />
            <el-table-column label="检测周期数" prop="cycleCount" width="100" />
            <el-table-column label="检测频次数" prop="frequency" width="100">
              <template #default="{ row }">
                <el-tooltip content="检测频次(次/检测周期)" placement="top">
                  <span>{{ row.frequency }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="样品数" prop="sampleCount" width="80">
              <template #default="{ row }">
                <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                  <span>{{ row.sampleCount }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="是否分包" prop="isSubcontract" width="100">
              <template #default="{ row }">
                <el-tag :type="row.isSubcontract ? 'warning' : 'success'">
                  {{ row.isSubcontract ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table-column>

          <!-- 其他信息 -->
          <el-table-column label="其他信息" align="center">
            <el-table-column label="备注" prop="remark" width="150" show-overflow-tooltip />
          </el-table-column>
        </el-table>

        <div class="fee-summary">
          <span>检测明细项目项目数：{{ quotationDetailData.length }}</span>
        </div>
      </div>

      <el-divider content-position="left">附件列表</el-divider>
      <el-table :data="form.attachments" style="width: 100%" border>
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="文件名称" prop="fileName" />
        <el-table-column label="文件大小" prop="fileSize" width="120">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button v-if="active > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="active < 2" type="primary" @click="nextStep(active)">下一步</el-button>
        <el-button v-if="active === 2" type="primary" @click="submitForm">保存草稿</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { addProjectQuotation, searchCustomer } from "@/api/quotation/projectQuotation"
import { getCustomer } from "@/api/customer"
import { listUser } from "@/api/system/user"
import QuotationDetail from '@/components/QuotationDetail/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前步骤
const active = ref(0)
// 表单引用
const basicFormRef = ref(null)
// 服务类型选择
const serviceTypes = ref([])
// 文件列表
const fileList = ref([])

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 重置数据
    resetData()
    // 初始化数据
    initData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听服务类型变化
watch(serviceTypes, (newVal) => {
  form.serviceType = newVal.join(',')
})

// 审批人选项
const approverOptions = ref([])

// 检测明细项目数据
const quotationDetailData = ref([])
// 是否显示检测明细项目配置
const quotationDetailOpen = ref(false)
// 检测明细项目组件的内部状态数据
const quotationDetailState = ref({
  categories: [],
  testItemsData: {},
  samplingPointsData: {}
})

// 表单对象
const form = reactive({
  projectName: '',
  contractCode: '',
  serviceType: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  approvers: [],
  status: '0', // 默认为草稿状态
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
  commissionDate: [
    { required: true, message: '委托日期不能为空', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '服务类型不能为空', trigger: 'change' }
  ]
}

// 重置数据
function resetData() {
  // 重置表单对象
  Object.assign(form, {
    projectName: '',
    contractCode: '',
    serviceType: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    approvers: [],
    status: '0', // 默认为草稿状态
    remark: '',
    items: [],
    attachments: []
  })

  // 重置服务类型
  serviceTypes.value = []

  // 重置文件列表
  fileList.value = []

  // 重置当前步骤
  active.value = 0
}

// 初始化数据
function initData() {
  // 获取审批人选项
  getApproverOptions()
}

// 获取审批人选项
function getApproverOptions() {
  listUser().then(response => {
    approverOptions.value = response.data.rows.map(user => ({
      value: user.postName || user.userName,
      label: `${user.userName}(${user.postName || '无岗位'})`
    }))
  }).catch(error => {
    console.error('获取用户列表失败:', error)
  })
}

// 查询客户列表
function queryCustomers(queryString, cb) {
  if (!queryString) {
    cb([])
    return
  }

  // 使用正确的API接口
  searchCustomer({ keyword: queryString }).then(response => {
    const customers = response.data.map(item => {
      return { value: item.name, id: item.id }
    })
    cb(customers)
  }).catch(error => {
    console.error('查询客户失败:', error)
    cb([])
  })
}

// 选择客户
function handleCustomerSelect(item) {
  form.customerId = item.id
  getCustomer(item.id).then(response => {
    const customer = response.data
    form.customerAddress = customer.address
    form.customerContact = customer.contactName
    form.customerPhone = customer.contactPhone
  })
}

// 打开检测明细项目配置
function openQuotationDetail() {
  quotationDetailOpen.value = true
}

// 处理检测明细项目配置确认
function handleQuotationDetailConfirm(data, componentState) {
  quotationDetailData.value = data
  // 保存组件的内部状态，以便下次打开时恢复
  if (componentState) {
    quotationDetailState.value = {
      categories: componentState.categories || [],
      testItemsData: componentState.testItemsData || {},
      samplingPointsData: componentState.samplingPointsData || {}
    }
  }
  quotationDetailOpen.value = false
  ElMessage.success('检测明细项目配置成功')
}



// 下一步
function nextStep(step) {
  if (step === 0) {
    // 验证基本信息表单
    basicFormRef.value.validate(valid => {
      if (valid) {
        active.value = 1
      }
    })
  } else if (step === 1) {
    // 验证检测明细项目
    if (quotationDetailData.value.length === 0) {
      ElMessage.warning('请配置检测明细项目')
      return
    }
    active.value = 2
  }
}

// 上一步
function prevStep() {
  active.value--
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 保存草稿表单
function submitForm() {
  // 验证检测明细项目
  if (quotationDetailData.value.length === 0) {
    ElMessage.warning('请配置检测明细项目')
    return
  }

  // 转换审批人数组为字符串
  const formData = { ...form }
  if (Array.isArray(formData.approvers)) {
    formData.approvers = formData.approvers.join(',')
  }

  // 将检测明细项目数据添加到表单中
  formData.items = quotationDetailData.value

  // 确保基本信息字段有值
  formData.projectName = formData.projectName || ''
  formData.contractCode = formData.contractCode || ''
  formData.customerName = formData.customerName || ''
  formData.customerAddress = formData.customerAddress || ''
  formData.customerContact = formData.customerContact || ''
  formData.customerPhone = formData.customerPhone || ''
  formData.inspectedParty = formData.inspectedParty || ''
  formData.inspectedContact = formData.inspectedContact || ''
  formData.inspectedPhone = formData.inspectedPhone || ''
  formData.inspectedAddress = formData.inspectedAddress || ''
  formData.projectManager = formData.projectManager || ''
  formData.marketManager = formData.marketManager || ''
  formData.technicalManager = formData.technicalManager || ''
  formData.remark = formData.remark || ''
  console.log("提交检测明细项目数据:", formData)

  // 确保日期字段格式正确
  if (formData.commissionDate && typeof formData.commissionDate !== 'string') {
    // 如果是日期对象，转换为字符串
    formData.commissionDate = formData.commissionDate.toISOString().split('T')[0]
  }

  // 保存草稿表单
  addProjectQuotation(formData).then(() => {
    ElMessage.success('添加成功')
    closeDialog()
    emit('refresh')
  }).catch(error => {
    console.error('添加失败:', error)
    if (error.response && error.response.data) {
      ElMessage.error(`添加失败: ${error.response.data.message || '未知错误'}`)
      console.error('错误详情:', error.response.data)
    } else {
      ElMessage.error('添加失败，请检查表单数据')
    }
  })
}


</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.dialog-footer .el-button {
  margin-left: 10px;
}

.fee-summary {
  margin-top: 20px;
  text-align: right;
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>