<template>
  <!-- 新增项目报价弹框 -->
  <el-dialog title="新增项目报价" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-steps :active="active" finish-status="success" simple style="margin-bottom: 20px">
      <el-step title="基本信息" />
      <el-step title="检测项目" />
      <el-step title="确认提交" />
    </el-steps>

    <!-- 基本信息表单 -->
    <div v-if="active === 0">
      <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="合同编号" prop="contractCode">
          <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
        </el-form-item>
        <el-form-item label="服务类型" prop="serviceType">
          <el-checkbox-group v-model="serviceTypes">
            <el-checkbox label="采样检测" />
            <el-checkbox label="送样检测" />
          </el-checkbox-group>
        </el-form-item>
        <el-divider content-position="left">客户信息</el-divider>
        <el-form-item label="客户名称" prop="customerName">
          <el-autocomplete
            v-model="form.customerName"
            :fetch-suggestions="queryCustomers"
            placeholder="请输入客户名称"
            @select="handleCustomerSelect"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="客户地址">
          <el-input v-model="form.customerAddress" placeholder="客户地址" disabled />
        </el-form-item>
        <el-form-item label="客户联系人">
          <el-input v-model="form.customerContact" placeholder="客户联系人" disabled />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="form.customerPhone" placeholder="联系电话" disabled />
        </el-form-item>
        <el-divider content-position="left">受检方信息</el-divider>
        <el-form-item label="受检方企业名称" prop="inspectedParty">
          <el-input v-model="form.inspectedParty" placeholder="请输入受检方企业名称" />
        </el-form-item>
        <el-form-item label="受检方联系人" prop="inspectedContact">
          <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
        </el-form-item>
        <el-form-item label="受检方联系电话" prop="inspectedPhone">
          <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
        </el-form-item>
        <el-form-item label="受检方详细地址" prop="inspectedAddress">
          <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
        </el-form-item>
        <el-divider content-position="left">项目信息</el-divider>
        <el-form-item label="项目负责人" prop="projectManager">
          <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
        </el-form-item>
        <el-form-item label="市场负责人" prop="marketManager">
          <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
        </el-form-item>
        <el-form-item label="项目技术人" prop="technicalManager">
          <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
        </el-form-item>
        <el-form-item label="委托日期" prop="commissionDate">
          <el-date-picker
            v-model="form.commissionDate"
            type="date"
            placeholder="选择委托日期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="项目审批人" prop="approvers">
          <el-select
            v-model="form.approvers"
            multiple
            placeholder="请选择项目审批人"
            style="width: 100%"
          >
            <el-option
              v-for="item in approverOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
    </div>

    <!-- 检测项目表单 -->
    <div v-if="active === 1">
      <div style="margin-bottom: 20px">
        <el-button type="primary" @click="addItem">添加检测项目</el-button>
      </div>
      <el-table :data="form.items" style="width: 100%" border>
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="服务类型" prop="serviceType" width="120">
          <template #default="scope">
            <el-select v-model="scope.row.serviceType" placeholder="请选择服务类型" style="width: 100%">
              <el-option label="采样检测" value="采样检测" />
              <el-option label="送样检测" value="送样检测" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="检测类别" prop="category" width="120">
          <template #default="scope">
            <el-select
              v-model="scope.row.category"
              placeholder="请选择检测类别"
              style="width: 100%"
              @change="handleCategoryChange(scope.row)"
            >
              <el-option
                v-for="item in categoryOptions"
                :key="item.category"
                :label="item.category"
                :value="item.category"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="检测参数" prop="parameter" width="120">
          <template #default="scope">
            <el-select
              v-model="scope.row.parameter"
              placeholder="请选择检测参数"
              style="width: 100%"
              @change="handleParameterChange(scope.row)"
            >
              <el-option
                v-for="item in getLocalParameterOptions(scope.row.category)"
                :key="item.parameter"
                :label="item.parameter"
                :value="item.parameter"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="检测方法" prop="method" width="120">
          <template #default="scope">
            <el-select
              v-model="scope.row.method"
              placeholder="请选择检测方法"
              style="width: 100%"
              @change="handleMethodChange(scope.row)"
            >
              <el-option
                v-for="item in getLocalMethodOptions(scope.row.category, scope.row.parameter)"
                :key="item.method"
                :label="item.method"
                :value="item.method"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="检测编号" prop="testCode" width="120">
          <template #default="scope">
            <el-input v-model="scope.row.testCode" placeholder="自动带出" disabled />
          </template>
        </el-table-column>
        <el-table-column label="报价编号" prop="priceCode" width="120">
          <template #default="scope">
            <el-input v-model="scope.row.priceCode" placeholder="自动带出" disabled />
          </template>
        </el-table-column>
        <el-table-column label="点位名称" prop="pointName" width="120">
          <template #default="scope">
            <el-input v-model="scope.row.pointName" placeholder="请输入点位名称" />
          </template>
        </el-table-column>
        <el-table-column label="点位数" prop="pointCount" width="80">
          <template #default="scope">
            <el-input-number v-model="scope.row.pointCount" :min="1" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="周期类型" prop="cycleType" width="100">
          <template #default="scope">
            <el-select v-model="scope.row.cycleType" placeholder="请选择" style="width: 100%">
              <el-option label="日" value="日" />
              <el-option label="周" value="周" />
              <el-option label="月" value="月" />
              <el-option label="季" value="季" />
              <el-option label="年" value="年" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="周期数" prop="cycleCount" width="80">
          <template #default="scope">
            <el-input-number v-model="scope.row.cycleCount" :min="1" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="频次数" prop="frequency" width="80">
          <template #default="scope">
            <el-input-number v-model="scope.row.frequency" :min="1" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="样品数" prop="sampleCount" width="80">
          <template #default="scope">
            <el-input-number v-model="scope.row.sampleCount" :min="1" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="采样单价" prop="samplingPrice" width="100">
          <template #default="scope">
            <el-input-number v-model="scope.row.samplingPrice" :precision="2" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="检测单价" prop="testingPrice" width="100">
          <template #default="scope">
            <el-input-number v-model="scope.row.testingPrice" :precision="2" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="差旅费单价" prop="travelPrice" width="100">
          <template #default="scope">
            <el-input-number v-model="scope.row.travelPrice" :precision="2" @change="calculateItemTotal(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="总价" prop="totalPrice" width="100">
          <template #default="scope">
            <el-input-number v-model="scope.row.totalPrice" :precision="2" disabled />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="scope">
            <el-button type="danger" icon="Delete" circle @click="removeItem(scope.$index)" />
          </template>
        </el-table-column>
      </el-table>

      <div style="margin-top: 20px">
        <el-upload
          class="upload-demo"
          action="/api/common/upload"
          :on-success="handleUploadSuccess"
          :on-remove="handleRemove"
          :file-list="fileList"
          multiple
        >
          <el-button type="primary">上传采样方案附件</el-button>
          <template #tip>
            <div class="el-upload__tip">支持上传采样方案附件，作为报价单补充材料</div>
          </template>
        </el-upload>
      </div>
    </div>

    <!-- 确认提交 -->
    <div v-if="active === 2">
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="项目名称">{{ form.projectName }}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{ form.contractCode }}</el-descriptions-item>
        <el-descriptions-item label="服务类型">{{ form.serviceType }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ form.customerName }}</el-descriptions-item>
        <el-descriptions-item label="受检方企业名称">{{ form.inspectedParty }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{ form.projectManager }}</el-descriptions-item>
        <el-descriptions-item label="市场负责人">{{ form.marketManager }}</el-descriptions-item>
        <el-descriptions-item label="项目技术人">{{ form.technicalManager }}</el-descriptions-item>
        <el-descriptions-item label="委托日期">{{ form.commissionDate }}</el-descriptions-item>
        <el-descriptions-item label="项目审批人">{{ form.approvers }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ form.remark }}</el-descriptions-item>
      </el-descriptions>

      <el-table :data="form.items" style="width: 100%" border>
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="服务类型" prop="serviceType" width="120" />
        <el-table-column label="检测类别" prop="category" width="120" />
        <el-table-column label="检测参数" prop="parameter" width="120" />
        <el-table-column label="检测方法" prop="method" width="120" />
        <el-table-column label="检测编号" prop="testCode" width="120" />
        <el-table-column label="报价编号" prop="priceCode" width="120" />
        <el-table-column label="点位名称" prop="pointName" width="120" />
        <el-table-column label="点位数" prop="pointCount" width="80" />
        <el-table-column label="周期类型" prop="cycleType" width="100" />
        <el-table-column label="周期数" prop="cycleCount" width="80" />
        <el-table-column label="频次数" prop="frequency" width="80" />
        <el-table-column label="样品数" prop="sampleCount" width="80" />
        <el-table-column label="总价" prop="totalPrice" width="100" />
      </el-table>

      <el-divider content-position="left">附件列表</el-divider>
      <el-table :data="form.attachments" style="width: 100%" border>
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="文件名称" prop="fileName" />
        <el-table-column label="文件大小" prop="fileSize" width="120">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button v-if="active > 0" @click="prevStep">上一步</el-button>
        <el-button v-if="active < 2" type="primary" @click="nextStep(active)">下一步</el-button>
        <el-button v-if="active === 2" type="primary" @click="submitForm">保存草稿</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { addProjectQuotation, searchCustomer } from "@/api/quotation/projectQuotation"
import { getCustomer } from "@/api/customer"
import { getCategoryOptions, getParameterOptions, getMethodOptions, getTechnicalManualByParams } from "@/api/basedata/technicalManual"
import { getPriceListByTestCode } from "@/api/basedata/priceList"
import { echoRequest } from "@/api/debug/debug"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前步骤
const active = ref(0)
// 表单引用
const basicFormRef = ref(null)
// 服务类型选择
const serviceTypes = ref([])
// 文件列表
const fileList = ref([])

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    // 重置数据
    resetData()
    // 初始化数据
    initData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 监听服务类型变化
watch(serviceTypes, (newVal) => {
  form.serviceType = newVal.join(',')
})

// 审批人选项
const approverOptions = ref([
  { value: '张三', label: '张三' },
  { value: '李四', label: '李四' },
  { value: '王五', label: '王五' }
])

// 检测类别选项
const categoryOptions = ref([])
// 检测参数选项映射
const parameterOptionsMap = ref({})
// 检测方法选项映射
const methodOptionsMap = ref({})

// 表单对象
const form = reactive({
  projectName: '',
  contractCode: '',
  serviceType: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  approvers: [],
  status: '0', // 默认为草稿状态
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
  commissionDate: [
    { required: true, message: '委托日期不能为空', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '服务类型不能为空', trigger: 'change' }
  ]
}

// 重置数据
function resetData() {
  // 重置表单对象
  Object.assign(form, {
    projectName: '',
    contractCode: '',
    serviceType: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    approvers: [],
    status: '0', // 默认为草稿状态
    remark: '',
    items: [],
    attachments: []
  })

  // 重置服务类型
  serviceTypes.value = []

  // 重置文件列表
  fileList.value = []

  // 重置当前步骤
  active.value = 0
}

// 初始化数据
function initData() {
  // 获取检测类别选项
  getCategoryOptions().then(response => {
    categoryOptions.value = response.data
  })
}

// 获取本地检测参数选项
function getLocalParameterOptions(category) {
  return parameterOptionsMap.value[category] || []
}

// 获取本地检测方法选项
function getLocalMethodOptions(category, parameter) {
  const key = `${category}-${parameter}`
  return methodOptionsMap.value[key] || []
}

// 查询客户列表
function queryCustomers(queryString, cb) {
  searchCustomer({keyword: queryString}).then(response => {
    const customers = response.data.map(item => {
      return { value: item.name, id: item.id }
    })
    cb(customers)
  })
}

// 选择客户
function handleCustomerSelect(item) {
  form.customerId = item.id
  getCustomer(item.id).then(response => {
    const customer = response.data
    form.customerAddress = customer.address
    form.customerContact = customer.contactName
    form.customerPhone = customer.contactPhone
  })
}

// 添加检测项目
function addItem() {
  form.items.push({
    serviceType: serviceTypes.value.length > 0 ? serviceTypes.value[0] : '送样检测', // 确保有默认值
    category: '',
    parameter: '',
    method: '',
    testCode: '',
    priceCode: '',
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1,
    samplingPrice: 0,
    testingPrice: 0, // 这个字段是必填的，但会在选择方法后自动填充
    travelPrice: 0,
    totalPrice: 0,
    remark: '' // 添加备注字段
  })
}

// 移除检测项目
function removeItem(index) {
  form.items.splice(index, 1)
  calculateTotal()
}

// 处理检测类别变化
function handleCategoryChange(row) {
  row.parameter = ''
  row.method = ''
  row.testCode = ''
  row.priceCode = ''

  // 获取检测参数选项
  getParameterOptions({ category: row.category }).then(response => {
    parameterOptionsMap.value[row.category] = response.data
  })
}

// 处理检测参数变化
function handleParameterChange(row) {
  row.method = ''
  row.testCode = ''
  row.priceCode = ''

  // 获取检测方法选项
  getMethodOptions({ category: row.category, parameter: row.parameter }).then(response => {
    const key = `${row.category}-${row.parameter}`
    methodOptionsMap.value[key] = response.data
  })
}

// 处理检测方法变化
function handleMethodChange(row) {
  // 检查参数是否有效
  if (!row.category || !row.parameter || !row.method) {
    ElMessage.warning('检测类别、参数和方法不能为空')
    return
  }

  // 根据检测类别、参数和方法获取检测编号
  getTechnicalManualByParams({
    category: row.category,
    parameter: row.parameter,
    method: row.method
  }).then(response => {
    const technicalManual = response.data
    row.testCode = technicalManual.testCode

    // 根据检测编号获取报价信息
    getPriceListByTestCode(row.testCode).then(priceResponse => {
      const priceList = priceResponse.data
      if (priceList) {
        row.priceCode = priceList.priceCode
        row.samplingPrice = priceList.samplingPrice || 0
        row.testingPrice = priceList.testingPrice || 0
        row.travelPrice = priceList.travelPrice || 0
        calculateItemTotal(row)
      }
    }).catch(error => {
      console.error('获取价格信息失败:', error)
      ElMessage.error('获取价格信息失败，请检查检测编号是否正确')
    })
  }).catch(error => {
    console.error('获取技术手册详情失败:', error)
    if (error.response && error.response.data) {
      ElMessage.error(`获取技术手册详情失败: ${error.response.data.message || '未知错误'}`)
    } else {
      ElMessage.error('获取技术手册详情失败，请检查检测类别、参数和方法是否正确')
    }
    // 如果没有找到对应的技术手册，清空检测编号和价格信息
    row.testCode = ''
    row.priceCode = ''
    row.samplingPrice = 0
    row.testingPrice = 0
    row.travelPrice = 0
    calculateItemTotal(row)
  })
}

// 计算项目明细总价
function calculateItemTotal(row) {
  // 确保所有数值都是数字类型
  const pointCount = Number(row.pointCount) || 1
  const cycleCount = Number(row.cycleCount) || 1
  const frequency = Number(row.frequency) || 1
  const sampleCount = Number(row.sampleCount) || 1

  const samplingPrice = Number(row.samplingPrice) || 0
  const testingPrice = Number(row.testingPrice) || 0
  const travelPrice = Number(row.travelPrice) || 0

  // 计算总价并保留两位小数
  const total = (samplingPrice + testingPrice + travelPrice) * pointCount * cycleCount * frequency * sampleCount
  row.totalPrice = parseFloat(total.toFixed(2))

  // 更新回原始对象
  row.pointCount = pointCount
  row.cycleCount = cycleCount
  row.frequency = frequency
  row.sampleCount = sampleCount
  row.samplingPrice = samplingPrice
  row.testingPrice = testingPrice
  row.travelPrice = travelPrice

  // 计算总金额
  calculateTotal()
}

// 计算总金额
function calculateTotal() {
  let total = 0
  form.items.forEach(item => {
    total += Number(item.totalPrice) || 0
  })
  form.totalAmount = parseFloat(total.toFixed(2))
}

// 处理文件上传成功
function handleUploadSuccess(response, file, uploadFileList) {
  // 更新本地文件列表
  fileList.value = uploadFileList

  // 添加到附件列表
  form.attachments.push({
    fileName: file.name,
    filePath: response.data,
    fileSize: Number(file.size) || 0,
    fileType: file.type || ''
  })
}

// 处理文件移除
function handleRemove(file, uploadFileList) {
  // 更新本地文件列表
  fileList.value = uploadFileList

  // 从附件列表中移除
  const index = form.attachments.findIndex(item => item.fileName === file.name)
  if (index !== -1) {
    form.attachments.splice(index, 1)
  }
}

// 格式化文件大小
function formatFileSize(size) {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 下一步
function nextStep(step) {
  if (step === 0) {
    // 验证基本信息表单
    basicFormRef.value.validate(valid => {
      if (valid) {
        if (serviceTypes.value.length === 0) {
          ElMessage.warning('请选择服务类型')
          return
        }
        active.value = 1
      }
    })
  } else if (step === 1) {
    // 验证检测项目
    if (form.items.length === 0) {
      ElMessage.warning('请添加至少一个检测项目')
      return
    }

    // 验证每个检测项目的必填字段
    let valid = true
    form.items.forEach((item, index) => {
      if (!item.serviceType || !item.category || !item.parameter || !item.method) {
        ElMessage.warning(`第 ${index + 1} 行检测项目信息不完整`)
        valid = false
      }
    })

    if (valid) {
      active.value = 2
    }
  }
}

// 上一步
function prevStep() {
  active.value--
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 保存草稿表单
function submitForm() {
  // 验证检测项目
  if (form.items.length === 0) {
    ElMessage.warning('请添加至少一个检测项目')
    return
  }

  // 验证每个检测项目的必填字段
  let valid = true
  form.items.forEach((item, index) => {
    if (!item.serviceType || !item.category || !item.parameter || !item.method) {
      ElMessage.warning(`第 ${index + 1} 行检测项目信息不完整`)
      valid = false
    }

    // 确保检测单价有值
    if (!item.testingPrice || item.testingPrice <= 0) {
      ElMessage.warning(`第 ${index + 1} 行检测单价必须大于0`)
      valid = false
    }

    // 确保数值字段是数值类型
    item.pointCount = Number(item.pointCount) || 1
    item.cycleCount = Number(item.cycleCount) || 1
    item.frequency = Number(item.frequency) || 1
    item.sampleCount = Number(item.sampleCount) || 1
    item.samplingPrice = Number(item.samplingPrice) || 0
    item.testingPrice = Number(item.testingPrice) || 0
    item.travelPrice = Number(item.travelPrice) || 0
    item.totalPrice = Number(item.totalPrice) || 0

    // 确保所有字段都有值
    item.pointName = item.pointName || ''
    item.cycleType = item.cycleType || '日'
    item.testCode = item.testCode || ''
    item.priceCode = item.priceCode || ''
    item.remark = item.remark || ''
  })

  if (!valid) {
    return
  }

  // 转换审批人数组为字符串
  const formData = { ...form }
  if (Array.isArray(formData.approvers)) {
    formData.approvers = formData.approvers.join(',')
  }

  // 确保服务类型有值
  if (!formData.serviceType && serviceTypes.value.length > 0) {
    formData.serviceType = serviceTypes.value.join(',')
  } else if (!formData.serviceType) {
    formData.serviceType = '送样检测' // 默认值
  }

  // 确保基本信息字段有值
  formData.projectName = formData.projectName || ''
  formData.contractCode = formData.contractCode || ''
  formData.customerName = formData.customerName || ''
  formData.customerAddress = formData.customerAddress || ''
  formData.customerContact = formData.customerContact || ''
  formData.customerPhone = formData.customerPhone || ''
  formData.inspectedParty = formData.inspectedParty || ''
  formData.inspectedContact = formData.inspectedContact || ''
  formData.inspectedPhone = formData.inspectedPhone || ''
  formData.inspectedAddress = formData.inspectedAddress || ''
  formData.projectManager = formData.projectManager || ''
  formData.marketManager = formData.marketManager || ''
  formData.technicalManager = formData.technicalManager || ''
  formData.remark = formData.remark || ''

  // 确保日期字段格式正确
  if (formData.commissionDate && typeof formData.commissionDate !== 'string') {
    // 如果是日期对象，转换为字符串
    formData.commissionDate = formData.commissionDate.toISOString().split('T')[0]
  }

  // 确保附件字段格式正确
  formData.attachments.forEach(attachment => {
    attachment.fileName = attachment.fileName || ''
    attachment.filePath = attachment.filePath || ''
    attachment.fileSize = Number(attachment.fileSize) || 0
    attachment.fileType = attachment.fileType || ''
  })

  // 保存草稿表单
  addProjectQuotation(formData).then(() => {
    ElMessage.success('添加成功')
    closeDialog()
    emit('refresh')
  }).catch(error => {
    console.error('添加失败:', error)
    if (error.response && error.response.data) {
      ElMessage.error(`添加失败: ${error.response.data.message || '未知错误'}`)
      console.error('错误详情:', error.response.data)
    } else {
      ElMessage.error('添加失败，请检查表单数据')
    }
  })
}

// 初始化
onMounted(() => {
  // 获取检测类别选项
  getCategoryOptions().then(response => {
    categoryOptions.value = response.data
  })
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
.dialog-footer .el-button {
  margin-left: 10px;
}
</style>