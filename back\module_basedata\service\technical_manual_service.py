from datetime import datetime
from fastapi import Request, Response
from typing import List, Dict, Any, Optional

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from config.base_service import BaseService
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.technical_manual_vo import (
    AddTechnicalManualModel,
    BatchInputTechnicalManualModel,
    BatchUpdateTechnicalManualModel,
    EditTechnicalManualModel,
    TechnicalManualPageQueryModel,
    TechnicalManualQueryModel,
)
from sqlalchemy import and_, or_, select, distinct, update, func
from sqlalchemy.ext.asyncio import AsyncSession
from utils.export_util import ExportUtil
from utils.common_util import CamelCaseUtil


class TechnicalManualService(BaseService[TechnicalManual]):
    """
    技术手册管理模块服务层
    """

    async def generate_test_code(self):
        """
        生成检测编号

        :return: 检测编号
        """
        # 查询最大的检测编号
        stmt = select(func.max(TechnicalManual.test_code)).where(
            TechnicalManual.test_code.like('JC%'),
            TechnicalManual.del_flag == '0'
        )
        result = await self.db.execute(stmt)
        max_code = result.scalar()

        # 如果没有检测编号，则从JC000001开始
        if not max_code:
            return 'JC000001'

        # 提取数字部分并加1
        try:
            num = int(max_code[2:]) + 1
            return f'JC{num:06d}'
        except (ValueError, IndexError):
            # 如果解析失败，则从JC000001开始
            return 'JC000001'

    def __init__(self, db: AsyncSession):
        """
        初始化技术手册服务

        :param db: 数据库会话
        """
        super().__init__(TechnicalManual, db)
        self.db = db

    async def get_technical_manual_list(self, query_object: TechnicalManualQueryModel):
        """
        获取技术手册列表

        :param query_object: 查询参数对象
        :return: 技术手册列表
        """
        # 构建查询条件
        conditions = [TechnicalManual.del_flag == '0']

        if query_object.category:
            conditions.append(TechnicalManual.category == query_object.category)
        if query_object.parameter:
            conditions.append(TechnicalManual.parameter == query_object.parameter)
        if query_object.method:
            conditions.append(TechnicalManual.method == query_object.method)
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManual.category.like(f'%{query_object.keyword}%'),
                TechnicalManual.parameter.like(f'%{query_object.keyword}%'),
                TechnicalManual.method.like(f'%{query_object.keyword}%'),
                TechnicalManual.standard.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                TechnicalManual.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManual.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(TechnicalManual).where(and_(*conditions)).order_by(
            TechnicalManual.category, TechnicalManual.parameter, TechnicalManual.method
        )
        result = await self.db.execute(stmt)
        technical_manuals = result.scalars().all()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manuals)

    async def get_technical_manual_page(self, query_object: TechnicalManualPageQueryModel):
        """
        获取技术手册分页列表

        :param query_object: 查询参数对象
        :return: 技术手册分页列表
        """
        from utils.page_util import PageUtil

        # 构建查询条件
        conditions = [TechnicalManual.del_flag == '0']

        if query_object.category:
            conditions.append(TechnicalManual.category == query_object.category)
        if query_object.parameter:
            conditions.append(TechnicalManual.parameter == query_object.parameter)
        if query_object.method:
            conditions.append(TechnicalManual.method == query_object.method)
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManual.category.like(f'%{query_object.keyword}%'),
                TechnicalManual.parameter.like(f'%{query_object.keyword}%'),
                TechnicalManual.method.like(f'%{query_object.keyword}%'),
                TechnicalManual.standard.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                TechnicalManual.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManual.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(TechnicalManual).where(and_(*conditions)).order_by(
            TechnicalManual.category, TechnicalManual.parameter, TechnicalManual.method
        )
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_technical_manual_detail(self, id: int):
        """
        获取技术手册详情

        :param id: 技术手册ID
        :return: 技术手册详情
        """
        stmt = select(TechnicalManual).where(TechnicalManual.id == id)
        result = await self.db.execute(stmt)
        technical_manual = result.scalars().first()
        if not technical_manual:
            raise ServiceException(message=f'技术手册ID：{id}不存在')

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manual)

    async def check_technical_manual_unique(self, category: str, parameter: str, method: str, id: int = None):
        """
        检查技术手册是否唯一

        :param category: 检测类别
        :param parameter: 检测参数
        :param method: 检测方法
        :param id: 技术手册ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManual.category == category,
            TechnicalManual.parameter == parameter,
            TechnicalManual.method == method,
            TechnicalManual.del_flag == '0'
        ]

        if id:
            conditions.append(TechnicalManual.id != id)

        stmt = select(TechnicalManual).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def check_qualification_code_unique(self, qualification_code: str, id: int = None):
        """
        检查资质编号是否唯一

        :param qualification_code: 资质编号
        :param id: 技术手册ID
        :return: 是否唯一
        """
        if not qualification_code or qualification_code.strip() == '':
            return True

        conditions = [
            TechnicalManual.qualification_code == qualification_code.strip(),
            TechnicalManual.del_flag == '0'
        ]

        if id:
            conditions.append(TechnicalManual.id != id)

        stmt = select(TechnicalManual).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def add_technical_manual(self, _: Request, technical_manual_model: AddTechnicalManualModel, current_user: CurrentUserModel):
        """
        新增技术手册

        :param request: 请求对象
        :param technical_manual_model: 新增技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 新增结果
        """
        try:
            # 校验技术手册是否唯一
            if not await self.check_technical_manual_unique(
                technical_manual_model.category,
                technical_manual_model.parameter,
                technical_manual_model.method
            ):
                raise ServiceException(message=f'新增技术手册失败，该检测类别、参数和方法组合已存在')

            # 校验资质编号是否唯一
            if not await self.check_qualification_code_unique(technical_manual_model.qualification_code):
                raise ServiceException(message=f'新增技术手册失败，资质编号已存在')

            # 生成检测编号
            test_code = await self.generate_test_code()

            # 创建技术手册对象
            technical_manual = TechnicalManual(
                test_code=test_code,
                category=technical_manual_model.category,
                parameter=technical_manual_model.parameter,
                method=technical_manual_model.method,
                description=technical_manual_model.description,
                # 新增字段
                classification=technical_manual_model.classification,
                qualification_code=technical_manual_model.qualification_code.strip() if technical_manual_model.qualification_code else None,
                limitation_scope=technical_manual_model.limitation_scope,
                common_alias=technical_manual_model.common_alias,
                qualification_date=technical_manual_model.qualification_date,
                # 原有字段
                status=technical_manual_model.status,
                create_by=current_user.user.user_name if current_user and current_user.user else '',
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else '',
                update_time=datetime.now(),
                remark=technical_manual_model.remark
            )

            # 新增技术手册
            self.db.add(technical_manual)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='新增成功',
                result={'id': technical_manual.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("添加技术手册失败", e)
            raise ServiceException(message=f'新增技术手册失败: {str(e)}')

    async def edit_technical_manual(self, _: Request, technical_manual_model: EditTechnicalManualModel, current_user: CurrentUserModel):
        """
        编辑技术手册

        :param request: 请求对象
        :param technical_manual_model: 编辑技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 编辑结果
        """
        try:
            # 校验技术手册是否存在
            stmt = select(TechnicalManual).where(TechnicalManual.id == technical_manual_model.id)
            result = await self.db.execute(stmt)
            technical_manual = result.scalars().first()
            if not technical_manual:
                raise ServiceException(message=f'技术手册ID：{technical_manual_model.id}不存在')

            # 校验技术手册是否唯一
            if not await self.check_technical_manual_unique(
                technical_manual_model.category,
                technical_manual_model.parameter,
                technical_manual_model.method,
                technical_manual_model.id
            ):
                raise ServiceException(message=f'修改技术手册失败，该检测类别、参数和方法组合已存在')

            # 校验资质编号是否唯一
            if not await self.check_qualification_code_unique(technical_manual_model.qualification_code, technical_manual_model.id):
                raise ServiceException(message=f'修改技术手册失败，资质编号已存在')

            # 更新技术手册对象
            technical_manual.category = technical_manual_model.category
            technical_manual.parameter = technical_manual_model.parameter
            technical_manual.method = technical_manual_model.method
            technical_manual.description = technical_manual_model.description
            # 新增字段
            technical_manual.classification = technical_manual_model.classification
            technical_manual.qualification_code = technical_manual_model.qualification_code.strip() if technical_manual_model.qualification_code else None
            technical_manual.limitation_scope = technical_manual_model.limitation_scope
            technical_manual.common_alias = technical_manual_model.common_alias
            technical_manual.qualification_date = technical_manual_model.qualification_date
            # 原有字段
            technical_manual.status = technical_manual_model.status
            technical_manual.update_by = current_user.user.user_name if current_user and current_user.user else ''
            technical_manual.update_time = datetime.now()
            technical_manual.remark = technical_manual_model.remark

            # 更新技术手册
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='修改成功',
                result={'id': technical_manual.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("修改技术手册失败", e)
            raise ServiceException(message=f'修改技术手册失败: {str(e)}')

    async def batch_update_technical_manual(self, _: Request, batch_update_model: BatchUpdateTechnicalManualModel, current_user: CurrentUserModel):
        """
        批量更新技术手册

        :param request: 请求对象
        :param batch_update_model: 批量更新技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 批量更新结果
        """
        try:
            # 检查ID列表是否为空
            if not batch_update_model.ids:
                raise ServiceException(message='批量更新失败，ID列表不能为空')

            # 构建更新条件
            conditions = [
                TechnicalManual.id.in_(batch_update_model.ids),
                TechnicalManual.del_flag == '0'
            ]

            # 构建更新值
            values = {}
            # 新增字段
            if batch_update_model.classification is not None:
                values['classification'] = batch_update_model.classification
            if batch_update_model.qualification_code is not None:
                values['qualification_code'] = batch_update_model.qualification_code.strip() if batch_update_model.qualification_code else None
            if batch_update_model.limitation_scope is not None:
                values['limitation_scope'] = batch_update_model.limitation_scope
            if batch_update_model.common_alias is not None:
                values['common_alias'] = batch_update_model.common_alias
            if batch_update_model.qualification_date is not None:
                values['qualification_date'] = batch_update_model.qualification_date
            # 原有字段
            if batch_update_model.status is not None:
                values['status'] = batch_update_model.status
            if batch_update_model.remark is not None:
                values['remark'] = batch_update_model.remark

            # 添加更新时间和更新人
            values['update_by'] = current_user.user.user_name if current_user and current_user.user else ''
            values['update_time'] = datetime.now()

            # 如果没有要更新的值，则返回错误
            if len(values) <= 2:  # 只有update_by和update_time
                raise ServiceException(message='批量更新失败，没有要更新的值')

            # 执行批量更新
            stmt = update(TechnicalManual).where(and_(*conditions)).values(**values)
            result = await self.db.execute(stmt)

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message=f'批量更新成功，共更新{result.rowcount}条记录',
                result={'count': result.rowcount}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("批量更新技术手册失败", e)
            raise ServiceException(message=f'批量更新技术手册失败: {str(e)}')

    async def batch_input_technical_manual(self, _: Request, batch_input_model: BatchInputTechnicalManualModel, current_user: CurrentUserModel):
        """
        批量录入技术手册

        :param request: 请求对象
        :param batch_input_model: 批量录入技术手册对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 批量录入结果
        """
        try:
            # 解析输入数据
            categories = [cat.strip() for cat in batch_input_model.categories.split(',') if cat.strip()]
            parameters = [param.strip() for param in batch_input_model.parameters.split(',') if param.strip()]
            methods = [method.strip() for method in batch_input_model.methods.split(',') if method.strip()]

            # 检查输入数据长度是否一致
            if len(categories) != len(parameters) or len(parameters) != len(methods):
                raise ServiceException(message='批量录入失败，检测类别、参数和方法的数量必须一致')

            # 批量创建技术手册
            success_count = 0
            error_messages = []

            for i in range(len(categories)):
                try:
                    category = categories[i]
                    parameter = parameters[i]
                    method = methods[i]

                    # 校验技术手册是否唯一
                    if not await self.check_technical_manual_unique(category, parameter, method):
                        error_messages.append(f'第{i+1}条记录：检测类别、参数和方法组合已存在')
                        continue

                    # 校验资质编号是否唯一
                    if not await self.check_qualification_code_unique(batch_input_model.qualification_code):
                        error_messages.append(f'第{i+1}条记录：资质编号已存在')
                        continue

                    # 生成检测编号
                    test_code = await self.generate_test_code()

                    # 创建技术手册对象
                    technical_manual = TechnicalManual(
                        test_code=test_code,
                        category=category,
                        parameter=parameter,
                        method=method,
                        # 新增字段
                        classification=batch_input_model.classification,
                        qualification_code=batch_input_model.qualification_code.strip() if batch_input_model.qualification_code else None,
                        limitation_scope=batch_input_model.limitation_scope,
                        common_alias=batch_input_model.common_alias,
                        qualification_date=batch_input_model.qualification_date,
                        # 原有字段
                        status=batch_input_model.status,
                        create_by=current_user.user.user_name if current_user and current_user.user else '',
                        create_time=datetime.now(),
                        update_by=current_user.user.user_name if current_user and current_user.user else '',
                        update_time=datetime.now(),
                        remark=batch_input_model.remark
                    )

                    # 新增技术手册
                    self.db.add(technical_manual)
                    success_count += 1

                except Exception as e:
                    error_messages.append(f'第{i+1}条记录：{str(e)}')

            # 提交事务
            await self.db.commit()

            # 返回结果
            message = f'批量录入完成，成功{success_count}条'
            if error_messages:
                message += f'，失败{len(error_messages)}条：{"; ".join(error_messages)}'

            return CrudResponseModel(
                is_success=True,
                message=message,
                result={'success_count': success_count, 'error_count': len(error_messages)}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("批量录入技术手册失败", e)
            raise ServiceException(message=f'批量录入技术手册失败: {str(e)}')

    async def export_technical_manual(self, query_object: TechnicalManualQueryModel, export_type: str) -> Response:
        """
        导出技术手册

        :param query_object: 查询参数对象
        :param export_type: 导出类型（excel, pdf, word）
        :return: 导出文件响应
        """
        # 获取技术手册列表
        technical_manuals = await self.get_technical_manual_list(query_object)

        # 转换为字典列表
        data = []
        for manual in technical_manuals:
            # 将SQLAlchemy模型转换为字典
            manual_dict = {
                'id': manual.id,
                'test_code': manual.test_code or '',
                'category': manual.category,
                'parameter': manual.parameter,
                'method': manual.method,
                'description': manual.description or '',
                # 新增字段
                'classification': manual.classification or '',
                'qualification_code': manual.qualification_code or '',
                'limitation_scope': manual.limitation_scope or '',
                'common_alias': manual.common_alias or '',
                'qualification_date': manual.qualification_date.strftime('%Y-%m-%d') if manual.qualification_date else '',
                # 原有字段
                'status': '启用' if manual.status == '0' else '停用',
                'create_time': manual.create_time.strftime('%Y-%m-%d %H:%M:%S') if manual.create_time else '',
                'create_by': manual.create_by,
                'remark': manual.remark or ''
            }
            data.append(manual_dict)

        # 定义表头映射
        headers = {
            'id': 'ID',
            'test_code': '检测编号',
            'category': '检测类别',
            'parameter': '检测参数',
            'method': '检测方法',
            'description': '描述',
            # 新增字段
            'classification': '分类',
            'qualification_code': '资质编号',
            'limitation_scope': '限制范围',
            'common_alias': '常用别名',
            'qualification_date': '取得资质时间',
            # 原有字段
            'status': '状态',
            'create_time': '创建时间',
            'create_by': '创建人',
            'remark': '备注'
        }

        # 根据导出类型导出文件
        if export_type.lower() == 'excel':
            return await ExportUtil.export_excel(data, '技术手册', headers)
        elif export_type.lower() == 'pdf':
            return await ExportUtil.export_pdf(data, '技术手册', headers, '技术手册列表')
        elif export_type.lower() == 'word':
            return await ExportUtil.export_word(data, '技术手册', headers, '技术手册列表')
        else:
            raise ServiceException(message=f'不支持的导出类型: {export_type}')
