<template>
  <!-- 修改项目信息弹框 -->
  <el-dialog :title="'修改项目信息 - ' + form.projectName" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-tabs v-model="activeTab">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="合同编号" prop="contractCode">
            <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
          </el-form-item>
          <el-form-item label="服务类型" prop="serviceType">
            <el-checkbox-group v-model="serviceTypes">
              <el-checkbox label="采样检测" />
              <el-checkbox label="送样检测" />
            </el-checkbox-group>
          </el-form-item>
          <el-divider content-position="left">客户信息</el-divider>
          <el-form-item label="客户名称" prop="customerName">
            <el-autocomplete
              v-model="form.customerName"
              :fetch-suggestions="queryCustomers"
              placeholder="请输入客户名称"
              @select="handleCustomerSelect"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="客户地址">
            <el-input v-model="form.customerAddress" placeholder="客户地址" />
          </el-form-item>
          <el-form-item label="客户联系人">
            <el-input v-model="form.customerContact" placeholder="客户联系人" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.customerPhone" placeholder="联系电话" />
          </el-form-item>
          <el-divider content-position="left">受检方信息</el-divider>
          <el-form-item label="受检方企业名称" prop="inspectedParty">
            <el-input v-model="form.inspectedParty" placeholder="请输入受检方企业名称" />
          </el-form-item>
          <el-form-item label="受检方联系人" prop="inspectedContact">
            <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
          </el-form-item>
          <el-form-item label="受检方联系电话" prop="inspectedPhone">
            <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
          </el-form-item>
          <el-form-item label="受检方详细地址" prop="inspectedAddress">
            <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
          </el-form-item>
          <el-divider content-position="left">项目信息</el-divider>
          <el-form-item label="项目负责人" prop="projectManager">
            <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
          </el-form-item>
          <el-form-item label="市场负责人" prop="marketManager">
            <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
          </el-form-item>
          <el-form-item label="项目技术人" prop="technicalManager">
            <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
          </el-form-item>
          <el-form-item label="委托日期" prop="commissionDate">
            <el-date-picker
              v-model="form.commissionDate"
              type="date"
              placeholder="选择委托日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="项目审批人" prop="approvers">
            <el-select
              v-model="form.approvers"
              multiple
              placeholder="请选择项目审批人"
              style="width: 100%"
            >
              <el-option
                v-for="item in approverOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 检测项目 -->
      <el-tab-pane label="检测项目" name="items">
        <div style="margin-bottom: 20px">
          <el-button type="primary" @click="openQuotationDetail">配置检测明细项目</el-button>
        </div>

        <!-- 检测明细项目表格 -->
        <div v-if="quotationDetailData.length > 0">
          <el-table :data="quotationDetailData" style="width: 100%" border>
            <!-- 检测项目信息 -->
            <el-table-column label="检测项目" align="center">
              <el-table-column label="分类" prop="classification" width="100" show-overflow-tooltip />
              <el-table-column label="二级分类" prop="category" width="120" show-overflow-tooltip />
              <el-table-column label="指标" prop="parameter" width="150" show-overflow-tooltip fixed="left" />
              <el-table-column label="方法" prop="method" width="200" show-overflow-tooltip />
            </el-table-column>

            <!-- 采样信息 -->
            <el-table-column label="采样信息" align="center">
              <el-table-column label="样品来源" prop="sampleSource" width="100" />
              <el-table-column label="点位名称" prop="pointName" width="120">
                <template #default="{ row }">
                  <el-tooltip :content="row.pointName" placement="top">
                    <span>{{ row.pointName }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="点位数" prop="pointCount" width="80">
                <template #default="{ row }">
                  <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                    <span>{{ row.pointCount }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="检测周期类型" prop="cycleType" width="120" />
              <el-table-column label="检测周期数" prop="cycleCount" width="100" />
              <el-table-column label="检测频次数" prop="frequency" width="100">
                <template #default="{ row }">
                  <el-tooltip content="检测频次(次/检测周期)" placement="top">
                    <span>{{ row.frequency }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="样品数" prop="sampleCount" width="80">
                <template #default="{ row }">
                  <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                    <span>{{ row.sampleCount }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="是否分包" prop="isSubcontract" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.isSubcontract ? 'warning' : 'success'">
                    {{ row.isSubcontract ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table-column>

            <!-- 其他信息 -->
            <el-table-column label="其他信息" align="center">
              <el-table-column label="备注" prop="remark" width="150" show-overflow-tooltip />
            </el-table-column>
          </el-table>

          <div class="fee-summary">
            <span>检测明细项目数：{{ quotationDetailData.length }}</span>
          </div>
        </div>

        <div v-else class="empty-state">
          <el-empty description="请配置检测明细项目" />
        </div>

        <!-- 检测明细项目配置组件 -->
        <QuotationDetail
          :visible="quotationDetailOpen"
          :initial-data="quotationDetailState"
          @update:visible="quotationDetailOpen = $event"
          @confirm="handleQuotationDetailConfirm"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotation } from "@/api/quotation/projectQuotation"
import { listCustomer, getCustomer } from "@/api/customer"
import { getCategoryOptions, getParameterOptions, getMethodOptions, getTechnicalManualByParams } from "@/api/basedata/technicalManual"
import { getPriceListByTestCode } from "@/api/basedata/priceList"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前激活的标签页
const activeTab = ref('basic')
// 表单引用
const basicFormRef = ref(null)
// 服务类型选择
const serviceTypes = ref([])

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置数据
    resetData()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  console.log('Dialog visible changed to:', newVal)
})

// 监听服务类型变化
watch(serviceTypes, (newVal) => {
  form.value.serviceType = newVal.join(',')
})

// 审批人选项
const approverOptions = ref([
  { value: '张三', label: '张三' },
  { value: '李四', label: '李四' },
  { value: '王五', label: '王五' }
])

// 检测类别选项
const categoryOptions = ref([])
// 检测参数选项映射
const parameterOptionsMap = ref({})
// 检测方法选项映射
const methodOptionsMap = ref({})

// 表单对象
const form = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  contractCode: '',
  serviceType: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  approvers: [],
  status: '',
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '服务类型不能为空', trigger: 'change' }
  ]
}

// 重置数据
function resetData() {
  // 重置表单对象
  form.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    contractCode: '',
    serviceType: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    approvers: [],
    status: '',
    remark: '',
    items: [],
    attachments: []
  }

  // 重置服务类型
  serviceTypes.value = []
}

// 获取项目报价详情
function getQuotationDetail(id) {
  getProjectQuotation(id).then(response => {
    const data = response.data

    // 填充表单数据
    form.value = {
      id: data.id,
      projectName: data.projectName,
      projectCode: data.projectCode,
      contractCode: data.contractCode,
      serviceType: data.serviceType,
      customerId: data.customerId,
      customerName: data.customerName,
      customerAddress: data.customerAddress,
      customerContact: data.customerContact,
      customerPhone: data.customerPhone,
      inspectedParty: data.inspectedParty,
      inspectedContact: data.inspectedContact,
      inspectedPhone: data.inspectedPhone,
      inspectedAddress: data.inspectedAddress,
      projectManager: data.projectManager,
      marketManager: data.marketManager,
      technicalManager: data.technicalManager,
      commissionDate: data.commissionDate,
      approvers: data.approvers ? data.approvers.split(',') : [],
      status: data.status,
      remark: data.remark,
      items: data.items || [],
      attachments: data.attachments || []
    }

    // 设置服务类型
    if (data.serviceType) {
      serviceTypes.value = data.serviceType.split(',')
    }

    // 获取检测类别选项
    getCategoryOptions().then(response => {
      categoryOptions.value = response.data

      // 预加载检测参数和方法选项
      form.value.items.forEach(item => {
        if (item.category) {
          getParameterOptions({ category: item.category }).then(response => {
            parameterOptionsMap.value[item.category] = response.data
          })

          if (item.parameter) {
            const key = `${item.category}-${item.parameter}`
            getMethodOptions({ category: item.category, parameter: item.parameter }).then(response => {
              methodOptionsMap.value[key] = response.data
            })
          }
        }
      })
    })
  })
}

// 查询客户列表
function queryCustomers(queryString, cb) {
  const params = {
    customerName: queryString
  }
  listCustomer(params).then(response => {
    const customers = response.data.map(item => {
      return { value: item.customerName, id: item.id }
    })
    cb(customers)
  })
}

// 选择客户
function handleCustomerSelect(item) {
  form.value.customerId = item.id
  getCustomer(item.id).then(response => {
    const customer = response.data
    form.value.customerAddress = customer.address
    form.value.customerContact = customer.contactName
    form.value.customerPhone = customer.contactPhone
  })
}

// 获取本地检测参数选项
function getLocalParameterOptions(category) {
  return parameterOptionsMap.value[category] || []
}

// 获取本地检测方法选项
function getLocalMethodOptions(category, parameter) {
  const key = `${category}-${parameter}`
  return methodOptionsMap.value[key] || []
}

// 添加检测项目
function addItem() {
  form.value.items.push({
    serviceType: serviceTypes.value.length > 0 ? serviceTypes.value[0] : '送样检测',
    category: '',
    parameter: '',
    method: '',
    testCode: '',
    priceCode: '',
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1,
    samplingPrice: 0,
    testingPrice: 0,
    travelPrice: 0,
    totalPrice: 0,
    remark: ''
  })
}

// 移除检测项目
function removeItem(index) {
  form.value.items.splice(index, 1)
}

// 处理检测类别变化
function handleCategoryChange(row) {
  row.parameter = ''
  row.method = ''
  row.testCode = ''
  row.priceCode = ''

  // 获取检测参数选项
  getParameterOptions({ category: row.category }).then(response => {
    parameterOptionsMap.value[row.category] = response.data
  })
}

// 处理检测参数变化
function handleParameterChange(row) {
  row.method = ''
  row.testCode = ''
  row.priceCode = ''

  // 获取检测方法选项
  getMethodOptions({ category: row.category, parameter: row.parameter }).then(response => {
    const key = `${row.category}-${row.parameter}`
    methodOptionsMap.value[key] = response.data
  })
}

// 处理检测方法变化
function handleMethodChange(row) {
  // 检查参数是否有效
  if (!row.category || !row.parameter || !row.method) {
    ElMessage.warning('检测类别、参数和方法不能为空')
    return
  }

  // 根据检测类别、参数和方法获取检测编号
  getTechnicalManualByParams({
    category: row.category,
    parameter: row.parameter,
    method: row.method
  }).then(response => {
    const technicalManual = response.data
    row.testCode = technicalManual.testCode

    // 根据检测编号获取报价信息
    getPriceListByTestCode(row.testCode).then(priceResponse => {
      const priceList = priceResponse.data
      if (priceList) {
        row.priceCode = priceList.priceCode
        row.samplingPrice = priceList.samplingPrice || 0
        row.testingPrice = priceList.testingPrice || 0
        row.travelPrice = priceList.travelPrice || 0
        calculateItemTotal(row)
      }
    }).catch(error => {
      console.error('获取价格信息失败:', error)
      ElMessage.error('获取价格信息失败，请检查检测编号是否正确')
    })
  }).catch(error => {
    console.error('获取技术手册详情失败:', error)
    if (error.response && error.response.data) {
      ElMessage.error(`获取技术手册详情失败: ${error.response.data.message || '未知错误'}`)
    } else {
      ElMessage.error('获取技术手册详情失败，请检查检测类别、参数和方法是否正确')
    }
    // 如果没有找到对应的技术手册，清空检测编号和价格信息
    row.testCode = ''
    row.priceCode = ''
    row.samplingPrice = 0
    row.testingPrice = 0
    row.travelPrice = 0
    calculateItemTotal(row)
  })
}

// 计算项目明细总价
function calculateItemTotal(row) {
  // 确保所有数值都是数字类型
  const pointCount = Number(row.pointCount) || 1
  const cycleCount = Number(row.cycleCount) || 1
  const frequency = Number(row.frequency) || 1
  const sampleCount = Number(row.sampleCount) || 1

  const samplingPrice = Number(row.samplingPrice) || 0
  const testingPrice = Number(row.testingPrice) || 0
  const travelPrice = Number(row.travelPrice) || 0

  // 计算总价并保留两位小数
  const total = (samplingPrice + testingPrice + travelPrice) * pointCount * cycleCount * frequency * sampleCount
  row.totalPrice = parseFloat(total.toFixed(2))

  // 更新回原始对象
  row.pointCount = pointCount
  row.cycleCount = cycleCount
  row.frequency = frequency
  row.sampleCount = sampleCount
  row.samplingPrice = samplingPrice
  row.testingPrice = testingPrice
  row.travelPrice = travelPrice
}

// 获取检测项目费用合计
function getTestingFeeTotal() {
  let total = 0
  form.value.items.forEach(item => {
    total += Number(item.totalPrice) || 0
  })
  return total.toFixed(2)
}

// 关闭对话框
function closeDialog() {
  console.log('关闭对话框，当前 dialogVisible:', dialogVisible.value)
  dialogVisible.value = false
  console.log('关闭对话框后，dialogVisible 设置为:', dialogVisible.value)
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 提交表单
function submitForm() {
  basicFormRef.value.validate(valid => {
    if (valid) {
      // 验证检测项目
      if (form.value.items.length === 0) {
        ElMessage.warning('请添加至少一个检测项目')
        return
      }

      // 验证每个检测项目的必填字段
      let itemsValid = true
      form.value.items.forEach((item, index) => {
        if (!item.serviceType || !item.category || !item.parameter || !item.method) {
          ElMessage.warning(`第 ${index + 1} 行检测项目信息不完整`)
          itemsValid = false
        }
      })

      if (!itemsValid) {
        return
      }

      // 转换审批人数组为字符串
      const formData = { ...form.value }
      if (Array.isArray(formData.approvers)) {
        formData.approvers = formData.approvers.join(',')
      }

      // 调用保存接口
      updateProjectQuotation(formData).then(() => {
        ElMessage.success('保存成功')
        console.log('保存成功，准备关闭对话框')
        dialogVisible.value = false
        emit('update:visible', false)
        emit('refresh')
      }).catch(error => {
        console.error('保存失败:', error)
        if (error.response && error.response.data) {
          ElMessage.error(`保存失败: ${error.response.data.message || '未知错误'}`)
        } else {
          ElMessage.error('保存失败，请检查表单数据')
        }
      })
    }
  })
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}
</style>
