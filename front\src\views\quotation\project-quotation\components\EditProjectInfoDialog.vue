<template>
  <!-- 修改项目信息弹框 -->
  <el-dialog :title="'修改项目信息 - ' + form.projectName" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-tabs v-model="activeTab">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="合同编号" prop="contractCode">
            <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
          </el-form-item>

          <el-divider content-position="left">客户信息</el-divider>
          <el-form-item label="客户名称" prop="customerName">
            <el-autocomplete
              v-model="form.customerName"
              :fetch-suggestions="queryCustomers"
              placeholder="请输入客户名称"
              @select="handleCustomerSelect"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="客户地址">
            <el-input v-model="form.customerAddress" placeholder="客户地址" />
          </el-form-item>
          <el-form-item label="客户联系人">
            <el-input v-model="form.customerContact" placeholder="客户联系人" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.customerPhone" placeholder="联系电话" />
          </el-form-item>
          <el-divider content-position="left">受检方信息</el-divider>
          <el-form-item label="受检方企业名称" prop="inspectedParty">
            <el-input v-model="form.inspectedParty" placeholder="请输入受检方企业名称" />
          </el-form-item>
          <el-form-item label="受检方联系人" prop="inspectedContact">
            <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
          </el-form-item>
          <el-form-item label="受检方联系电话" prop="inspectedPhone">
            <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
          </el-form-item>
          <el-form-item label="受检方详细地址" prop="inspectedAddress">
            <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
          </el-form-item>
          <el-divider content-position="left">项目信息</el-divider>
          <el-form-item label="项目负责人" prop="projectManager">
            <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
          </el-form-item>
          <el-form-item label="市场负责人" prop="marketManager">
            <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
          </el-form-item>
          <el-form-item label="项目技术人" prop="technicalManager">
            <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
          </el-form-item>
          <el-form-item label="委托日期" prop="commissionDate">
            <el-date-picker
              v-model="form.commissionDate"
              type="date"
              placeholder="选择委托日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="项目审批人" prop="approvers">
            <el-select
              v-model="form.approvers"
              multiple
              placeholder="请选择项目审批人"
              style="width: 100%"
            >
              <el-option
                v-for="item in approverOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </el-tab-pane>


    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotation, searchCustomer } from "@/api/quotation/projectQuotation"
import { getCustomer } from "@/api/customer"
import { listUser } from "@/api/system/user"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前激活的标签页
const activeTab = ref('basic')
// 表单引用
const basicFormRef = ref(null)
// 服务类型选择
const serviceTypes = ref([])

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置数据
    resetData()
    // 获取审批人选项
    getApproverOptions()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  console.log('Dialog visible changed to:', newVal)
})

// 监听服务类型变化
watch(serviceTypes, (newVal) => {
  form.value.serviceType = newVal.join(',')
})

// 审批人选项
const approverOptions = ref([])

// 表单对象
const form = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  contractCode: '',
  serviceType: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  approvers: [],
  status: '',
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '服务类型不能为空', trigger: 'change' }
  ]
}

// 重置数据
function resetData() {
  // 重置表单对象
  form.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    contractCode: '',
    serviceType: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    approvers: [],
    status: '',
    remark: '',
    items: [],
    attachments: []
  }

  // 重置服务类型
  serviceTypes.value = []
}

// 获取审批人选项
function getApproverOptions() {
  listUser().then(response => {
    approverOptions.value = response.data.rows.map(user => ({
      value: user.postName || user.userName,
      label: `${user.userName}(${user.postName || '无岗位'})`
    }))
  }).catch(error => {
    console.error('获取用户列表失败:', error)
  })
}

// 获取项目报价详情
function getQuotationDetail(id) {
  getProjectQuotation(id).then(response => {
    const data = response.data

    // 填充表单数据
    form.value = {
      id: data.id,
      projectName: data.projectName,
      projectCode: data.projectCode,
      contractCode: data.contractCode,
      serviceType: data.serviceType,
      customerId: data.customerId,
      customerName: data.customerName,
      customerAddress: data.customerAddress,
      customerContact: data.customerContact,
      customerPhone: data.customerPhone,
      inspectedParty: data.inspectedParty,
      inspectedContact: data.inspectedContact,
      inspectedPhone: data.inspectedPhone,
      inspectedAddress: data.inspectedAddress,
      projectManager: data.projectManager,
      marketManager: data.marketManager,
      technicalManager: data.technicalManager,
      commissionDate: data.commissionDate,
      approvers: data.approvers ? data.approvers.split(',') : [],
      status: data.status,
      remark: data.remark,
      items: data.items || [],
      attachments: data.attachments || []
    }

    // 设置服务类型
    if (data.serviceType) {
      serviceTypes.value = data.serviceType.split(',')
    }


  })
}

// 查询客户列表
function queryCustomers(queryString, cb) {
  if (!queryString) {
    cb([])
    return
  }

  // 使用正确的API接口
  searchCustomer({ keyword: queryString }).then(response => {
    const customers = response.data.map(item => {
      return { value: item.name, id: item.id }
    })
    cb(customers)
  }).catch(error => {
    console.error('查询客户失败:', error)
    cb([])
  })
}

// 选择客户
function handleCustomerSelect(item) {
  form.value.customerId = item.id
  getCustomer(item.id).then(response => {
    const customer = response.data
    form.value.customerAddress = customer.address
    form.value.customerContact = customer.contactName
    form.value.customerPhone = customer.contactPhone
  })
}



// 关闭对话框
function closeDialog() {
  console.log('关闭对话框，当前 dialogVisible:', dialogVisible.value)
  dialogVisible.value = false
  console.log('关闭对话框后，dialogVisible 设置为:', dialogVisible.value)
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 提交表单
function submitForm() {
  basicFormRef.value.validate(valid => {
    if (valid) {
      // 转换审批人数组为字符串
      const formData = { ...form.value }
      if (Array.isArray(formData.approvers)) {
        formData.approvers = formData.approvers.join(',')
      }

      // 调用保存接口
      updateProjectQuotation(formData).then(() => {
        ElMessage.success('保存成功')
        console.log('保存成功，准备关闭对话框')
        dialogVisible.value = false
        emit('update:visible', false)
        emit('refresh')
      }).catch(error => {
        console.error('保存失败:', error)
        if (error.response && error.response.data) {
          ElMessage.error(`保存失败: ${error.response.data.message || '未知错误'}`)
        } else {
          ElMessage.error('保存失败，请检查表单数据')
        }
      })
    }
  })
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}
</style>
