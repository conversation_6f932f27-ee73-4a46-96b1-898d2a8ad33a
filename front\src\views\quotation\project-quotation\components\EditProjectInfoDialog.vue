<template>
  <!-- 修改项目信息弹框 -->
  <el-dialog :title="'修改项目信息 - ' + form.projectName" v-model="dialogVisible" width="80%" append-to-body @closed="handleClosed" :before-close="closeDialog">
    <el-tabs v-model="activeTab">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <el-form ref="basicFormRef" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="合同编号" prop="contractCode">
            <el-input v-model="form.contractCode" placeholder="请输入合同编号" />
          </el-form-item>
          <el-divider content-position="left">客户信息</el-divider>
          <el-form-item label="客户名称" prop="customerName">
            <el-autocomplete
              v-model="form.customerName"
              :fetch-suggestions="queryCustomers"
              placeholder="请输入客户名称"
              @select="handleCustomerSelect"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="客户地址">
            <el-input v-model="form.customerAddress" placeholder="客户地址" />
          </el-form-item>
          <el-form-item label="客户联系人">
            <el-input v-model="form.customerContact" placeholder="客户联系人" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.customerPhone" placeholder="联系电话" />
          </el-form-item>
          <el-divider content-position="left">受检方信息</el-divider>
          <el-form-item label="受检方企业名称" prop="inspectedParty">
            <el-input v-model="form.inspectedParty" placeholder="请输入受检方企业名称" />
          </el-form-item>
          <el-form-item label="受检方联系人" prop="inspectedContact">
            <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
          </el-form-item>
          <el-form-item label="受检方联系电话" prop="inspectedPhone">
            <el-input v-model="form.inspectedPhone" placeholder="请输入受检方联系电话" />
          </el-form-item>
          <el-form-item label="受检方详细地址" prop="inspectedAddress">
            <el-input v-model="form.inspectedAddress" placeholder="请输入受检方详细地址" />
          </el-form-item>
          <el-divider content-position="left">项目信息</el-divider>
          <el-form-item label="项目负责人" prop="projectManager">
            <el-input v-model="form.projectManager" placeholder="请输入项目负责人" />
          </el-form-item>
          <el-form-item label="市场负责人" prop="marketManager">
            <el-input v-model="form.marketManager" placeholder="请输入市场负责人" />
          </el-form-item>
          <el-form-item label="项目技术人" prop="technicalManager">
            <el-input v-model="form.technicalManager" placeholder="请输入项目技术人" />
          </el-form-item>
          <el-form-item label="委托日期" prop="commissionDate">
            <el-date-picker
              v-model="form.commissionDate"
              type="date"
              placeholder="选择委托日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="项目审批人" prop="approvers">
            <el-select
              v-model="form.approvers"
              multiple
              placeholder="请选择项目审批人"
              style="width: 100%"
            >
              <el-option
                v-for="item in approverOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <!-- 检测项目 -->
      <el-tab-pane label="检测项目" name="items">
        <div style="margin-bottom: 20px">
          <el-button type="primary" @click="openQuotationDetail">配置检测明细项目</el-button>
        </div>

        <!-- 检测明细项目表格 -->
        <div v-if="quotationDetailData.length > 0">
          <el-table :data="quotationDetailData" style="width: 100%" border>
            <!-- 检测项目信息 -->
            <el-table-column label="检测项目" align="center">
              <el-table-column label="分类" prop="classification" width="100" show-overflow-tooltip />
              <el-table-column label="二级分类" prop="category" width="120" show-overflow-tooltip />
              <el-table-column label="指标" prop="parameter" width="150" show-overflow-tooltip fixed="left" />
              <el-table-column label="方法" prop="method" width="200" show-overflow-tooltip />
            </el-table-column>

            <!-- 采样信息 -->
            <el-table-column label="采样信息" align="center">
              <el-table-column label="样品来源" prop="sampleSource" width="100" />
              <el-table-column label="点位名称" prop="pointName" width="120">
                <template #default="{ row }">
                  <el-tooltip :content="row.pointName" placement="top">
                    <span>{{ row.pointName }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="点位数" prop="pointCount" width="80">
                <template #default="{ row }">
                  <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                    <span>{{ row.pointCount }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="检测周期类型" prop="cycleType" width="120" />
              <el-table-column label="检测周期数" prop="cycleCount" width="100" />
              <el-table-column label="检测频次数" prop="frequency" width="100">
                <template #default="{ row }">
                  <el-tooltip content="检测频次(次/检测周期)" placement="top">
                    <span>{{ row.frequency }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="样品数" prop="sampleCount" width="80">
                <template #default="{ row }">
                  <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                    <span>{{ row.sampleCount }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="是否分包" prop="isSubcontract" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.isSubcontract ? 'warning' : 'success'">
                    {{ row.isSubcontract ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table-column>

            <!-- 其他信息 -->
            <el-table-column label="其他信息" align="center">
              <el-table-column label="备注" prop="remark" width="150" show-overflow-tooltip />
            </el-table-column>
          </el-table>

          <div class="fee-summary">
            <span>检测明细项目数：{{ quotationDetailData.length }}</span>
          </div>
        </div>

        <div v-else class="empty-state">
          <el-empty description="请配置检测明细项目" />
        </div>

        <!-- 检测明细项目配置组件 -->
        <QuotationDetail
          :visible="quotationDetailOpen"
          :initial-data="quotationDetailState"
          @update:visible="quotationDetailOpen = $event"
          @confirm="handleQuotationDetailConfirm"
        />
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">保 存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getProjectQuotation, updateProjectQuotation } from "@/api/quotation/projectQuotation"
import { getCustomer } from "@/api/customer"
import { getCategoryOptions, getParameterOptions, getMethodOptions } from "@/api/basedata/technicalManual"
import { searchCustomer } from "@/api/quotation/projectQuotation"
import QuotationDetail from '@/components/QuotationDetail/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 当前激活的标签页
const activeTab = ref('basic')
// 表单引用
const basicFormRef = ref(null)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置数据
    resetData()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  console.log('Dialog visible changed to:', newVal)
})


// 审批人选项
const approverOptions = ref([
  { value: '张三', label: '张三' },
  { value: '李四', label: '李四' },
  { value: '王五', label: '王五' }
])

// 检测类别选项
const categoryOptions = ref([])
// 检测参数选项映射
const parameterOptionsMap = ref({})
// 检测方法选项映射
const methodOptionsMap = ref({})

// 检测明细项目相关数据
const quotationDetailOpen = ref(false)
const quotationDetailData = ref([])
const quotationDetailState = ref({
  categories: [],
  testItemsData: {},
  samplingPointsData: {}
})

// 表单对象
const form = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  contractCode: '',
  customerId: undefined,
  customerName: '',
  customerAddress: '',
  customerContact: '',
  customerPhone: '',
  inspectedParty: '',
  inspectedContact: '',
  inspectedPhone: '',
  inspectedAddress: '',
  projectManager: '',
  marketManager: '',
  technicalManager: '',
  commissionDate: '',
  approvers: [],
  status: '',
  remark: '',
  items: [],
  attachments: []
})

// 表单校验规则
const rules = {
  projectName: [
    { required: true, message: '项目名称不能为空', trigger: 'blur' }
  ],
}

// 重置数据
function resetData() {
  // 重置表单对象
  form.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    contractCode: '',
    customerId: undefined,
    customerName: '',
    customerAddress: '',
    customerContact: '',
    customerPhone: '',
    inspectedParty: '',
    inspectedContact: '',
    inspectedPhone: '',
    inspectedAddress: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    approvers: [],
    status: '',
    remark: '',
    items: [],
    attachments: []
  }

  // 重置检测明细项目数据
  quotationDetailData.value = []
  quotationDetailState.value = {
    categories: [],
    testItemsData: {},
    samplingPointsData: {}
  }
}

// 获取项目报价详情
function getQuotationDetail(id) {
  getProjectQuotation(id).then(response => {
    const data = response.data

    // 填充表单数据
    form.value = {
      id: data.id,
      projectName: data.projectName,
      projectCode: data.projectCode,
      contractCode: data.contractCode,
      customerId: data.customerId,
      customerName: data.customerName,
      customerAddress: data.customerAddress,
      customerContact: data.customerContact,
      customerPhone: data.customerPhone,
      inspectedParty: data.inspectedParty,
      inspectedContact: data.inspectedContact,
      inspectedPhone: data.inspectedPhone,
      inspectedAddress: data.inspectedAddress,
      projectManager: data.projectManager,
      marketManager: data.marketManager,
      technicalManager: data.technicalManager,
      commissionDate: data.commissionDate,
      approvers: data.approvers ? data.approvers.split(',') : [],
      status: data.status,
      remark: data.remark,
      items: data.items || [],
      attachments: data.attachments || []
    }

    // 转换现有的items数据为QuotationDetail组件所需的格式
    if (data.items && data.items.length > 0) {
      quotationDetailData.value = data.items.map(item => ({
        classification: item.classification || '',
        category: item.category || '',
        parameter: item.parameter || '',
        method: item.method || '',
        limitationScope: item.limitationScope || '',
        sampleSource: item.sampleSource || '',
        pointName: item.pointName || '',
        pointCount: item.pointCount || 1,
        cycleType: item.cycleType || '日',
        cycleCount: item.cycleCount || 1,
        frequency: item.frequency || 1,
        sampleCount: item.sampleCount || 1,
        isSubcontract: item.isSubcontract === '1' || item.isSubcontract === true,
        remark: item.remark || ''
      }))
    }

    // 获取检测类别选项
    getCategoryOptions().then(response => {
      categoryOptions.value = response.data

      // 预加载检测参数和方法选项
      form.value.items.forEach(item => {
        if (item.category) {
          getParameterOptions({ category: item.category }).then(response => {
            parameterOptionsMap.value[item.category] = response.data
          })

          if (item.parameter) {
            const key = `${item.category}-${item.parameter}`
            getMethodOptions({ category: item.category, parameter: item.parameter }).then(response => {
              methodOptionsMap.value[key] = response.data
            })
          }
        }
      })
    })
  })
}

// 打开检测明细项目配置
function openQuotationDetail() {
  quotationDetailOpen.value = true
}

// 处理检测明细项目配置确认
function handleQuotationDetailConfirm(data, componentState) {
  quotationDetailData.value = data
  // 保存组件的内部状态，以便下次打开时恢复
  if (componentState) {
    quotationDetailState.value = {
      categories: componentState.categories || [],
      testItemsData: componentState.testItemsData || {},
      samplingPointsData: componentState.samplingPointsData || {}
    }
  }

  // 将QuotationDetail组件的数据转换为form.items格式
  form.value.items = data.map(item => ({
    classification: item.classification || '',
    category: item.category || '',
    parameter: item.parameter || '',
    method: item.method || '',
    limitationScope: item.limitationScope || '',
    sampleSource: item.sampleSource || '',
    pointName: item.pointName || '',
    pointCount: item.pointCount || 1,
    cycleType: item.cycleType || '日',
    cycleCount: item.cycleCount || 1,
    frequency: item.frequency || 1,
    sampleCount: item.sampleCount || 1,
    isSubcontract: item.isSubcontract ? '1' : '0',
    remark: item.remark || ''
  }))

  quotationDetailOpen.value = false
  ElMessage.success('检测明细项目配置成功')
}

// 查询客户列表
function queryCustomers(queryString, cb) {
  if (!queryString) {
    cb([])
    return
  }

  searchCustomer({ keyword: queryString }).then(response => {
    const customers = response.data.map(item => {
      return { value: item.customerName, id: item.id }
    })
    cb(customers)
  }).catch(() => {
    cb([])
  })
}

// 选择客户
function handleCustomerSelect(item) {
  form.value.customerId = item.id
  getCustomer(item.id).then(response => {
    const customer = response.data
    form.value.customerAddress = customer.address
    form.value.customerContact = customer.contactName
    form.value.customerPhone = customer.contactPhone
  })
}



// 关闭对话框
function closeDialog() {
  console.log('关闭对话框，当前 dialogVisible:', dialogVisible.value)
  dialogVisible.value = false
  console.log('关闭对话框后，dialogVisible 设置为:', dialogVisible.value)
  emit('update:visible', false)
}

// 对话框关闭后的回调
function handleClosed() {
  // 重置数据
  resetData()
}

// 提交表单
function submitForm() {
  basicFormRef.value.validate(valid => {
    if (valid) {
      // 验证检测项目
      if (quotationDetailData.value.length === 0) {
        ElMessage.warning('请配置检测明细项目')
        activeTab.value = 'items'
        return
      }

      // 验证每个检测项目的必填字段
      let itemsValid = true
      quotationDetailData.value.forEach((item, index) => {
        if (!item.category || !item.parameter || !item.method) {
          ElMessage.warning(`第 ${index + 1} 行检测项目信息不完整，缺少必要的类别、参数或方法`)
          itemsValid = false
        }
      })

      if (!itemsValid) {
        activeTab.value = 'items'
        return
      }

      // 转换审批人数组为字符串
      const formData = { ...form.value }
      if (Array.isArray(formData.approvers)) {
        formData.approvers = formData.approvers.join(',')
      }

      // 调用保存接口
      updateProjectQuotation(formData).then(() => {
        ElMessage.success('保存成功')
        console.log('保存成功，准备关闭对话框')
        dialogVisible.value = false
        emit('update:visible', false)
        emit('refresh')
      }).catch(error => {
        console.error('保存失败:', error)
        if (error.response && error.response.data) {
          ElMessage.error(`保存失败: ${error.response.data.message || '未知错误'}`)
        } else {
          ElMessage.error('保存失败，请检查表单数据')
        }
      })
    }
  })
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}
</style>
