<template>
  <div>
    <!-- 查看项目报价弹框 -->
    <el-dialog :title="'[查看]项目报价 - ' + quotationData.projectName" :model-value="dialogVisible" @update:model-value="dialogVisible = $event" width="80%" append-to-body>
      <!-- 项目基本信息 -->
      <el-descriptions title="项目基本信息" :column="3" border>
        <el-descriptions-item label="项目名称">{{ quotationData.projectName }}</el-descriptions-item>
        <el-descriptions-item label="项目编号">{{ quotationData.projectCode }}</el-descriptions-item>
        <el-descriptions-item label="合同编号">{{ quotationData.contractCode }}</el-descriptions-item>
        <el-descriptions-item label="服务类型">{{ quotationData.serviceType }}</el-descriptions-item>
        <el-descriptions-item label="客户名称">{{ quotationData.customerName }}</el-descriptions-item>
        <el-descriptions-item label="受检方企业名称">{{ quotationData.inspectedParty }}</el-descriptions-item>
        <el-descriptions-item label="项目负责人">{{ quotationData.projectManager }}</el-descriptions-item>
        <el-descriptions-item label="市场负责人">{{ quotationData.marketManager }}</el-descriptions-item>
        <el-descriptions-item label="项目技术人">{{ quotationData.technicalManager }}</el-descriptions-item>
        <el-descriptions-item label="委托日期">{{ quotationData.commissionDate }}</el-descriptions-item>
        <el-descriptions-item label="项目状态">{{ getStatusLabel(quotationData.status) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider />

      <el-collapse v-model="activeNames">
        <!-- 检测项目费用明细 -->
        <el-collapse-item title="费用项1：检测项目费用明细" name="1">
          <el-table :data="quotationData.items" style="width: 100%" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="服务类型" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.serviceType" placement="top" :disabled="!scope.row.serviceType">
                  <div class="cell-content">{{ scope.row.serviceType || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="检测类别" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.category" placement="top" :disabled="!scope.row.category">
                  <div class="cell-content">{{ scope.row.category || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="检测参数" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.parameter" placement="top" :disabled="!scope.row.parameter">
                  <div class="cell-content">{{ scope.row.parameter || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="检测方法" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.method" placement="top" :disabled="!scope.row.method">
                  <div class="cell-content">{{ scope.row.method || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="检测编号" prop="testCode" width="120" />
            <el-table-column label="报价编号" prop="priceCode" width="120" />
            <el-table-column label="点位名称" width="120">
              <template #default="scope">
                <el-tooltip :content="scope.row.pointName" placement="top" :disabled="!scope.row.pointName">
                  <div class="cell-content">{{ scope.row.pointName || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="点位数" width="80">
              <template #default="scope">
                <span>{{ scope.row.pointCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="周期类型" width="100">
              <template #default="scope">
                <el-tooltip :content="scope.row.cycleType" placement="top" :disabled="!scope.row.cycleType">
                  <div class="cell-content">{{ scope.row.cycleType || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="周期数" width="80">
              <template #default="scope">
                <span>{{ scope.row.cycleCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="频次数" width="80">
              <template #default="scope">
                <span>{{ scope.row.frequency || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="样品数" width="80">
              <template #default="scope">
                <span>{{ scope.row.sampleCount || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="采样单价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.samplingPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="检测单价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.testingPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="差旅费单价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.travelPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总价" width="100">
              <template #default="scope">
                <span class="total-price">{{ formatCurrency(scope.row.totalPrice) }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="fee-summary">
            <span>检测项目费用合计：{{ getTestingFeeTotal() }}</span>
          </div>
          <div class="fee-summary">
            <span>折扣率(%)：</span>
            <el-input-number v-model="totalFeeData.discountRate" :min="0" :max="100" :precision="0" :disabled="!isEditable" @change="calculateTotalFee" />
          </div>
          <div class="fee-summary">
            <span>折后费用合计：{{ totalFeeData.discountedTestingFee }}</span>
          </div>
        </el-collapse-item>

        <!-- 项目其他费用表 -->
        <el-collapse-item title="费用项2：项目其他费用表" name="2">
          <el-table :data="quotationData.otherFees" style="width: 100%" border>
            <el-table-column label="序号" type="index" width="50" align="center" />
            <el-table-column label="费用名称" width="150">
              <template #default="scope">
                <el-tooltip :content="scope.row.feeName" placement="top" :disabled="!scope.row.feeName">
                  <div class="cell-content">{{ scope.row.feeName || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="数量" width="100">
              <template #default="scope">
                <span>{{ scope.row.quantity || '0' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.unitPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总价" width="100">
              <template #default="scope">
                <span>{{ formatCurrency(scope.row.totalPrice) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="scope">
                <el-tooltip :content="scope.row.remark" placement="top" :disabled="!scope.row.remark">
                  <div class="cell-content">{{ scope.row.remark || '-' }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <div class="fee-summary">
            <span>其他费用合计：{{ getOtherFeeTotal() }}</span>
          </div>
        </el-collapse-item>

        <!-- 项目总费用表 -->
        <el-collapse-item title="费用项3：项目总费用表" name="3">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目编号">{{ quotationData.projectCode }}</el-descriptions-item>
            <el-descriptions-item label="检测项目总折扣率">{{ totalFeeData.discountRate }}%</el-descriptions-item>
            <el-descriptions-item label="检测折后费用">
              <span class="fee-value">{{ formatCurrency(totalFeeData.discountedTestingFee) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="其他费用">
              <span class="fee-value">{{ formatCurrency(totalFeeData.otherFee) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠前总费用">
              <span class="fee-value">{{ formatCurrency(totalFeeData.totalFeeBeforeDiscount) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="税率">{{ totalFeeData.taxRate }}%</el-descriptions-item>
            <el-descriptions-item label="税费">
              <span class="fee-value">{{ formatCurrency(totalFeeData.tax) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠前总费用(税后)">
              <span class="fee-value">{{ formatCurrency(totalFeeData.totalFeeAfterTax) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="整体调整金额">
              <span class="fee-value" :class="{'negative-value': isNegative(totalFeeData.adjustmentAmount)}">
                {{ formatCurrency(totalFeeData.adjustmentAmount) }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠后总金额">
              <span class="final-amount">{{ formatCurrency(totalFeeData.finalAmount) }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">关 闭</el-button>
          <el-button type="primary" @click="handleEdit">编辑报价</el-button>
          <el-button type="success" @click="handleModify">修改项目</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改项目信息弹框 -->
    <EditProjectInfoDialog
      :visible="editProjectInfoVisible"
      @update:visible="editProjectInfoVisible = $event"
      :quotation-id="props.quotationId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { getProjectQuotation } from "@/api/quotation/projectQuotation"
import EditProjectInfoDialog from './EditProjectInfoDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  quotationId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'edit', 'refresh'])

// 对话框可见性
const dialogVisible = ref(false)
// 修改项目信息弹框可见性
const editProjectInfoVisible = ref(false)

// 监听props.visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.quotationId) {
    // 重置表单数据
    resetData()
    // 获取项目报价详情
    getQuotationDetail(props.quotationId)
  }
})

// 重置所有数据
function resetData() {
  quotationData.value = {
    id: undefined,
    projectName: '',
    projectCode: '',
    contractCode: '',
    serviceType: '',
    customerName: '',
    inspectedParty: '',
    projectManager: '',
    marketManager: '',
    technicalManager: '',
    commissionDate: '',
    status: '',
    items: [],
    otherFees: [],
    attachments: []
  }

  totalFeeData.value = {
    discountRate: 0,
    discountedTestingFee: 0,
    otherFee: 0,
    totalFeeBeforeDiscount: 0,
    taxRate: 0,
    tax: 0,
    totalFeeAfterTax: 0,
    adjustmentAmount: 0,
    finalAmount: 0
  }
}

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 折叠面板激活的项
const activeNames = ref(['1', '2', '3'])
// 报价数据对象
const quotationData = ref({
  id: undefined,
  projectName: '',
  projectCode: '',
  items: [],
  otherFees: []
})
// 总费用数据
const totalFeeData = ref({
  discountRate: 0,
  discountedTestingFee: 0,
  otherFee: 0,
  totalFeeBeforeDiscount: 0,
  taxRate: 0,
  tax: 0,
  totalFeeAfterTax: 0,
  adjustmentAmount: 0,
  finalAmount: 0
})

// 获取检测项目费用合计
function getTestingFeeTotal() {
  let total = 0
  quotationData.value.items.forEach(item => {
    total += item.totalPrice || 0
  })
  return total.toFixed(2)
}

// 获取其他费用合计
function getOtherFeeTotal() {
  let total = 0
  quotationData.value.otherFees.forEach(fee => {
    total += fee.totalPrice || 0
  })
  return total.toFixed(2)
}

// 获取状态标签
function getStatusLabel(status) {
  const statusMap = {
    '0': '草稿',
    '1': '待审核',
    '2': '已审核',
    '3': '已撤回',
    '4': '已拒绝'
  }
  return statusMap[status] || '未知'
}

// 格式化货币
function formatCurrency(value) {
  if (value === null || value === undefined) return '0.00';
  return parseFloat(value).toFixed(2);
}

// 判断是否为负值
function isNegative(value) {
  return parseFloat(value) < 0;
}

// 获取项目报价详情
function getQuotationDetail(id) {
  getProjectQuotation(id).then(response => {
    quotationData.value = response.data

    // 如果没有其他费用数据，初始化一个空数组
    if (!quotationData.value.otherFees) {
      quotationData.value.otherFees = []
    }

    // 如果有总费用数据，则使用后端数据
    if (quotationData.value.totalFee) {
      totalFeeData.value = quotationData.value.totalFee
    }
  })
}

// 关闭对话框
function closeDialog() {
  dialogVisible.value = false
}

// 编辑按钮点击事件
function handleEdit() {
  emit('edit', quotationData.value.id)
  closeDialog()
}

// 修改按钮点击事件
function handleModify() {
  console.log('修改按钮点击，当前 editProjectInfoVisible:', editProjectInfoVisible.value)
  editProjectInfoVisible.value = true
  console.log('修改按钮点击后，editProjectInfoVisible 设置为:', editProjectInfoVisible.value)
}

// 测试修改按钮
function testModify() {
  console.log('测试修改按钮点击')
  editProjectInfoVisible.value = !editProjectInfoVisible.value
  console.log('editProjectInfoVisible 切换为:', editProjectInfoVisible.value)
}

// 刷新数据
function handleRefresh() {
  getQuotationDetail(props.quotationId)
  emit('refresh')
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
  font-weight: bold;
  font-size: 16px;
}
.cell-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.el-table .cell {
  padding: 0 8px;
}
.total-price {
  font-weight: bold;
  color: #f56c6c;
}
.fee-value {
  font-weight: bold;
  color: #606266;
}
.negative-value {
  color: #67c23a;
}
.final-amount {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}
</style>
