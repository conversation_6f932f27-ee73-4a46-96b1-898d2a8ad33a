from fastapi import APIRouter, Depends, Request, Path, Query, Response
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional
from config.get_db import get_db
from utils.response_util import ResponseUtil
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_basedata.entity.vo.technical_manual_price_vo import (
    AddTechnicalManualPriceModel,
    EditTechnicalManualPriceModel,
    TechnicalManualPriceModel,
    TechnicalManualPricePageQueryModel,
    TechnicalManualPriceQueryModel,
    MethodCategoryOptionModel,
)
from module_basedata.service.technical_manual_price_service import TechnicalManualPriceService
from utils.page_util import PageResponseModel

# 创建路由
router = APIRouter(prefix='/basedata/technical-manual-price', tags=['技术手册价格管理'])


@router.get('/list', response_model=List[TechnicalManualPriceModel], summary='获取技术手册价格列表')
async def get_technical_manual_price_list(
    request: Request,
    query_params: TechnicalManualPriceQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册价格列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册价格列表
    """
    service = TechnicalManualPriceService(db)
    technical_manual_price_list = await service.get_technical_manual_price_list(query_params)
    return ResponseUtil.success(data=technical_manual_price_list)


@router.get('/page', response_model=PageResponseModel, summary='获取技术手册价格分页列表')
async def get_technical_manual_price_page(
    request: Request,
    query_params: TechnicalManualPricePageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册价格分页列表

    :param request: 请求对象
    :param query_params: 查询参数
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册价格分页列表
    """
    service = TechnicalManualPriceService(db)
    technical_manual_price_page = await service.get_technical_manual_price_page(query_params)
    return ResponseUtil.success(data=technical_manual_price_page)


@router.get('/{id}', response_model=TechnicalManualPriceModel, summary='获取技术手册价格详情')
async def get_technical_manual_price_detail(
    request: Request,
    id: int = Path(..., description='技术手册价格ID'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取技术手册价格详情

    :param request: 请求对象
    :param id: 技术手册价格ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 技术手册价格详情
    """
    service = TechnicalManualPriceService(db)
    technical_manual_price = await service.get_technical_manual_price_detail(id)
    return ResponseUtil.success(data=technical_manual_price)


@router.post('', response_model=CrudResponseModel, summary='新增技术手册价格')
async def add_technical_manual_price(
    request: Request,
    technical_manual_price: AddTechnicalManualPriceModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增技术手册价格

    :param request: 请求对象
    :param technical_manual_price: 新增技术手册价格对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 新增结果
    """
    service = TechnicalManualPriceService(db)
    result = await service.add_technical_manual_price(request, technical_manual_price, current_user)
    return ResponseUtil.success(data=result)


@router.put('', response_model=CrudResponseModel, summary='编辑技术手册价格')
async def edit_technical_manual_price(
    request: Request,
    technical_manual_price: EditTechnicalManualPriceModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    编辑技术手册价格

    :param request: 请求对象
    :param technical_manual_price: 编辑技术手册价格对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 编辑结果
    """
    service = TechnicalManualPriceService(db)
    result = await service.edit_technical_manual_price(request, technical_manual_price, current_user)
    return ResponseUtil.success(data=result)


@router.delete('/{id}', response_model=CrudResponseModel, summary='删除技术手册价格')
async def delete_technical_manual_price(
    request: Request,
    id: int = Path(..., description='技术手册价格ID'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    删除技术手册价格

    :param request: 请求对象
    :param id: 技术手册价格ID
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 删除结果
    """
    service = TechnicalManualPriceService(db)
    result = await service.delete_technical_manual_price(id, current_user)
    return ResponseUtil.success(data=result)


@router.get('/options/method-categories', summary='获取检测方法和类别选项')
async def get_method_category_options(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    获取检测方法和类别选项

    :param request: 请求对象
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 检测方法和类别选项列表
    """
    service = TechnicalManualPriceService(db)
    method_category_options = await service.get_method_category_options()
    return ResponseUtil.success(data=method_category_options)


@router.get('/export', summary='导出技术手册价格')
async def export_technical_manual_price(
    request: Request,
    export_type: str = Query(..., description='导出类型（excel, pdf, word）'),
    method: Optional[str] = Query(None, description='检测方法'),
    category: Optional[str] = Query(None, description='检测类别'),
    classification: Optional[str] = Query(None, description='分类'),
    keyword: Optional[str] = Query(None, description='关键字'),
    begin_time: Optional[str] = Query(None, description='开始时间'),
    end_time: Optional[str] = Query(None, description='结束时间'),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
) -> Response:
    """
    导出技术手册价格

    :param request: 请求对象
    :param export_type: 导出类型（excel, pdf, word）
    :param method: 检测方法
    :param category: 检测类别
    :param classification: 分类
    :param keyword: 关键字
    :param begin_time: 开始时间
    :param end_time: 结束时间
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 导出文件响应
    """
    # 构建查询参数
    query_params = TechnicalManualPriceQueryModel(
        method=method,
        category=category,
        classification=classification,
        keyword=keyword,
        begin_time=begin_time,
        end_time=end_time
    )

    # 导出技术手册价格
    service = TechnicalManualPriceService(db)
    return await service.export_technical_manual_price(query_params, export_type)
