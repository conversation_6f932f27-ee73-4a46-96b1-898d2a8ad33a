from typing import List, Dict, Any
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from config.database import get_db
from module_basedata.dao.technical_manual_category_dao import TechnicalManualCategoryDao
from module_basedata.service.technical_manual_category_service import TechnicalManualCategoryService
from module_basedata.entity.vo.technical_manual_category_vo import (
    TechnicalManualCategoryQueryModel,
    TechnicalManualCategoryAddModel,
    TechnicalManualCategoryEditModel,
    TechnicalManualCategoryDeleteModel
)
from module_admin.service.login_service import get_current_user
from module_admin.entity.vo.current_user import CurrentUserModel
from utils.response_util import ResponseUtil
from utils.log_util import LogUtil

router = APIRouter()


@router.get("/list", summary="获取技术手册类目列表")
def get_category_list(
    classification: str = Query(None, description="分类"),
    category: str = Query(None, description="检测类别"),
    status: str = Query(None, description="状态"),
    db: Session = Depends(get_db)
):
    """
    获取技术手册类目列表
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        query_model = TechnicalManualCategoryQueryModel(
            classification=classification,
            category=category,
            status=status
        )
        
        result = service.get_category_list(query_model)
        return ResponseUtil.success(data=result)
        
    except Exception as e:
        LogUtil.error(f"获取技术手册类目列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取技术手册类目列表失败: {str(e)}")


@router.get("/page", summary="分页查询技术手册类目")
def get_category_page(
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    classification: str = Query(None, description="分类"),
    category: str = Query(None, description="检测类别"),
    status: str = Query(None, description="状态"),
    db: Session = Depends(get_db)
):
    """
    分页查询技术手册类目
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        query_model = TechnicalManualCategoryQueryModel(
            page_num=page_num,
            page_size=page_size,
            classification=classification,
            category=category,
            status=status
        )
        
        result = service.get_category_page(query_model)
        return ResponseUtil.success(data=result.rows, total=result.total)
        
    except Exception as e:
        LogUtil.error(f"分页查询技术手册类目失败: {str(e)}")
        return ResponseUtil.error(message=f"分页查询技术手册类目失败: {str(e)}")


@router.get("/{category_id}", summary="获取技术手册类目详情")
def get_category_detail(category_id: int, db: Session = Depends(get_db)):
    """
    获取技术手册类目详情
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.get_category_by_id(category_id)
        return ResponseUtil.success(data=result)
        
    except ValueError as e:
        return ResponseUtil.error(message=str(e))
    except Exception as e:
        LogUtil.error(f"获取技术手册类目详情失败: {str(e)}")
        return ResponseUtil.error(message=f"获取技术手册类目详情失败: {str(e)}")


@router.post("/add", summary="新增技术手册类目")
def add_category(
    add_model: TechnicalManualCategoryAddModel,
    db: Session = Depends(get_db),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    新增技术手册类目
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.add_category(add_model, current_user)
        db.commit()
        
        return ResponseUtil.success(data=result, message="新增技术手册类目成功")
        
    except ValueError as e:
        db.rollback()
        return ResponseUtil.error(message=str(e))
    except Exception as e:
        db.rollback()
        LogUtil.error(f"新增技术手册类目失败: {str(e)}")
        return ResponseUtil.error(message=f"新增技术手册类目失败: {str(e)}")


@router.put("/edit", summary="编辑技术手册类目")
def edit_category(
    edit_model: TechnicalManualCategoryEditModel,
    db: Session = Depends(get_db),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    编辑技术手册类目
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.edit_category(edit_model, current_user)
        db.commit()
        
        return ResponseUtil.success(data=result, message="编辑技术手册类目成功")
        
    except ValueError as e:
        db.rollback()
        return ResponseUtil.error(message=str(e))
    except Exception as e:
        db.rollback()
        LogUtil.error(f"编辑技术手册类目失败: {str(e)}")
        return ResponseUtil.error(message=f"编辑技术手册类目失败: {str(e)}")


@router.delete("/delete", summary="删除技术手册类目")
def delete_categories(
    delete_model: TechnicalManualCategoryDeleteModel,
    db: Session = Depends(get_db),
    current_user: CurrentUserModel = Depends(get_current_user)
):
    """
    批量删除技术手册类目
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        count = service.delete_categories(delete_model)
        db.commit()
        
        return ResponseUtil.success(message=f"成功删除 {count} 个技术手册类目")
        
    except Exception as e:
        db.rollback()
        LogUtil.error(f"删除技术手册类目失败: {str(e)}")
        return ResponseUtil.error(message=f"删除技术手册类目失败: {str(e)}")


@router.get("/options/classifications", summary="获取所有分类")
def get_classifications(db: Session = Depends(get_db)):
    """
    获取所有分类
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.get_classifications()
        return ResponseUtil.success(data=result)
        
    except Exception as e:
        LogUtil.error(f"获取分类列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取分类列表失败: {str(e)}")


@router.get("/options/categories", summary="根据分类获取检测类别")
def get_categories_by_classification(
    classification: str = Query(..., description="分类"),
    db: Session = Depends(get_db)
):
    """
    根据分类获取检测类别列表
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.get_categories_by_classification(classification)
        return ResponseUtil.success(data=result)
        
    except Exception as e:
        LogUtil.error(f"获取检测类别列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取检测类别列表失败: {str(e)}")


@router.get("/options/all", summary="获取分类和检测类别的选项数据")
def get_category_options(db: Session = Depends(get_db)):
    """
    获取分类和检测类别的选项数据
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.get_category_options()
        return ResponseUtil.success(data=result)
        
    except Exception as e:
        LogUtil.error(f"获取选项数据失败: {str(e)}")
        return ResponseUtil.error(message=f"获取选项数据失败: {str(e)}")


@router.get("/select/list", summary="获取用于下拉选择的类目列表")
def get_category_select_list(db: Session = Depends(get_db)):
    """
    获取用于下拉选择的类目列表
    """
    try:
        dao = TechnicalManualCategoryDao(db)
        service = TechnicalManualCategoryService(dao)
        
        result = service.get_category_list_for_select()
        return ResponseUtil.success(data=result)
        
    except Exception as e:
        LogUtil.error(f"获取下拉选择列表失败: {str(e)}")
        return ResponseUtil.error(message=f"获取下拉选择列表失败: {str(e)}")
