from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from pydantic.alias_generators import to_camel


class TechnicalManualCategoryModel(BaseModel):
    """
    技术手册类目模型
    """
    
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    category_code: Optional[str] = Field(default=None, description="类目唯一编号")
    classification: str = Field(..., description="分类")
    category: str = Field(..., description="检测类别")
    status: Optional[str] = Field(default="0", description="状态（0正常 1停用）")
    del_flag: Optional[str] = Field(default="0", description="删除标志（0存在 2删除）")
    create_by: Optional[str] = Field(default="", description="创建者")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default="", description="更新者")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")
    remark: Optional[str] = Field(default="", description="备注")


class TechnicalManualCategoryQueryModel(BaseModel):
    """
    技术手册类目查询模型
    """
    
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    page_num: Optional[int] = Field(default=1, description="页码")
    page_size: Optional[int] = Field(default=10, description="每页数量")
    classification: Optional[str] = Field(default=None, description="分类")
    category: Optional[str] = Field(default=None, description="检测类别")
    status: Optional[str] = Field(default=None, description="状态")


class TechnicalManualCategoryAddModel(BaseModel):
    """
    技术手册类目新增模型
    """
    
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    classification: str = Field(..., description="分类")
    category: str = Field(..., description="检测类别")
    remark: Optional[str] = Field(default="", description="备注")


class TechnicalManualCategoryEditModel(BaseModel):
    """
    技术手册类目编辑模型
    """
    
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    id: int = Field(..., description="主键ID")
    classification: str = Field(..., description="分类")
    category: str = Field(..., description="检测类别")
    status: Optional[str] = Field(default="0", description="状态（0正常 1停用）")
    remark: Optional[str] = Field(default="", description="备注")


class TechnicalManualCategoryDeleteModel(BaseModel):
    """
    技术手册类目删除模型
    """
    
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    ids: List[int] = Field(..., description="主键ID列表")


class TechnicalManualCategoryListModel(BaseModel):
    """
    技术手册类目列表模型
    """
    
    model_config = ConfigDict(alias_generator=to_camel, populate_by_name=True)

    category_code: str = Field(..., description="类目唯一编号")
    classification: str = Field(..., description="分类")
    category: str = Field(..., description="检测类别")
