<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>检测项目选择组件演示</span>
        </div>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>功能说明</h3>
            <ul>
              <li>根据传入的检测类别查询技术手册数据</li>
              <li>左侧显示待选项目，支持分页和多选</li>
              <li>右侧显示已选项目，支持添加备注</li>
              <li>支持左右移动项目</li>
              <li>确认后返回所有已选项目的完整信息</li>
            </ul>
            
            <div class="demo-controls">
              <el-form :model="demoForm" label-width="100px">
                <el-form-item label="检测类别">
                  <el-input 
                    v-model="demoForm.category" 
                    placeholder="请输入检测类别，如：水质"
                    style="width: 200px;"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button 
                    type="primary" 
                    @click="openTestItemSelector"
                    :disabled="!demoForm.category"
                  >
                    打开检测项目选择器
                  </el-button>
                  <el-button @click="clearSelected">清空已选项目</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
          
          <el-col :span="12">
            <h3>选择结果</h3>
            <div v-if="selectedTestItems.length > 0" class="selected-result">
              <p><strong>已选择的检测项目（{{ selectedTestItems.length }}个）：</strong></p>
              <el-table :data="selectedTestItems" border style="width: 100%" max-height="300">
                <el-table-column label="检测参数" prop="parameter" width="120" show-overflow-tooltip />
                <el-table-column label="检测方法" prop="method" width="150" show-overflow-tooltip />
                <el-table-column label="资质编号" prop="qualificationCode" width="100" />
                <el-table-column label="备注" prop="remark" width="100" show-overflow-tooltip />
              </el-table>
            </div>
            <div v-else class="no-selection">
              <el-empty description="暂未选择任何检测项目" :image-size="100" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 检测项目选择组件 -->
    <test-item-selector
      :visible="testItemSelectOpen"
      @update:visible="testItemSelectOpen = $event"
      :category="demoForm.category"
      :selected-items="selectedTestItems"
      @confirm="handleTestItemConfirm"
      title="选择检测项目"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import TestItemSelector from '@/components/TestItemSelector/index.vue'

// 演示表单
const demoForm = reactive({
  category: '水质'
})

// 是否显示检测项目选择弹出层
const testItemSelectOpen = ref(false)
// 已选择的检测项目
const selectedTestItems = ref([])

/** 打开检测项目选择器 */
function openTestItemSelector() {
  if (!demoForm.category) {
    ElMessage.warning('请先输入检测类别')
    return
  }
  testItemSelectOpen.value = true
}

/** 处理检测项目选择确认 */
function handleTestItemConfirm(testItems) {
  selectedTestItems.value = [...testItems]
  
  // 显示选择结果
  if (testItems.length > 0) {
    ElMessage.success(`已选择 ${testItems.length} 个检测项目`)
    console.log('选中的检测项目:', testItems)
  } else {
    ElMessage.info('未选择任何检测项目')
  }
}

/** 清空已选项目 */
function clearSelected() {
  selectedTestItems.value = []
  ElMessage.info('已清空所有选择')
}
</script>

<style scoped>
.demo-content {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.selected-result {
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
  min-height: 100px;
}

.no-selection {
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

h3 {
  color: #303133;
  margin-bottom: 15px;
}

ul {
  color: #606266;
  line-height: 1.6;
}

li {
  margin-bottom: 8px;
}
</style>
