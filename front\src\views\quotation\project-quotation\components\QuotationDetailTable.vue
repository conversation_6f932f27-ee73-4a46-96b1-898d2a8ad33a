<template>
  <div>
    <div style="margin-bottom: 20px">
      <el-button type="primary" @click="openQuotationDetail">配置检测明细项目</el-button>
      <project-quotation-item-import @import-success="handleImportSuccess" style="margin-left: 10px;" />
    </div>
    <div v-if="quotationDetailData.length > 0">
      <el-table :data="quotationDetailData" style="width: 100%" border @filter-change="handleFilterChange">
        <!-- 检测项目信息 -->
        <el-table-column label="检测项目" align="center">
          <el-table-column
            label="分类" 
            prop="classification" 
            width="100" 
            sortable 
            show-overflow-tooltip
            :filters="getUniqueValues('classification')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="二级分类" 
            prop="category" 
            width="120" 
            sortable 
            show-overflow-tooltip
            :filters="getUniqueValues('category')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="指标" 
            prop="parameter" 
            width="150" 
            sortable 
            show-overflow-tooltip 
            fixed="left"
            :filters="getUniqueValues('parameter')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="方法" 
            prop="method" 
            width="200" 
            sortable 
            show-overflow-tooltip 
            :filters="getUniqueValues('method')"
            :filter-method="filterByValue"
          />
        </el-table-column>

        <!-- 采样信息 -->
        <el-table-column label="采样信息" align="center">
          <el-table-column 
            label="样品来源" 
            prop="sampleSource" 
            width="100"
            sortable 
            show-overflow-tooltip  
            :filters="getUniqueValues('sampleSource')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="点位名称" 
            prop="pointName" 
            width="120"
            sortable 
            show-overflow-tooltip  
            :filters="getUniqueValues('pointName')"
            :filter-method="filterByValue"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.pointName" placement="top">
                <span>{{ row.pointName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="点位数" 
            prop="pointCount" 
            width="80">
            <template #default="{ row }">
              <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                <span>{{ row.pointCount }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="周期类型" 
            prop="cycleType" 
            width="80"
            :filters="getUniqueValues('cycleType')"
            :filter-method="filterByValue"
          />
          <el-table-column 
            label="检测周期数" 
            prop="cycleCount" 
            width="100"
          />
          <el-table-column 
            label="检测频次数" 
            prop="frequency" 
            width="100">
            <template #default="{ row }">
              <el-tooltip content="检测频次(次/检测周期)" placement="top">
                <span>{{ row.frequency }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="样品数" 
            prop="sampleCount" 
            width="80">
            <template #default="{ row }">
              <el-tooltip content="一个采样点位的单次样品数量" placement="top">
                <span>{{ row.sampleCount }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column 
            label="是否分包" 
            prop="isSubcontract" 
            sortable 
            :filters="getUniqueValues('isSubcontract')"
            :filter-method="filterByValue"
            width="100">
            <template #default="{ row }">
              <el-tag :type="row.isSubcontract == '1' ? 'warning' : 'success'">
                {{ row.isSubcontract == '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 其他信息 -->
        <el-table-column label="其他信息" align="center">
          <el-table-column 
            label="备注" 
            prop="remark" 
            width="150" 
            show-overflow-tooltip
            :filters="getUniqueValues('remark')"
            :filter-method="filterByValue"
          />
        </el-table-column>
      </el-table>

      <div class="fee-summary">
        <span>检测明细项目数：{{ quotationDetailData.length }}</span>
      </div>
    </div>

    <div v-else class="empty-state">
      <el-empty description="请配置检测明细项目" />
    </div>

    <!-- 检测明细项目配置组件 -->
    <QuotationDetail
      :visible="quotationDetailOpen"
      :initial-data="quotationDetailState"
      @update:visible="quotationDetailOpen = $event"
      @confirm="handleQuotationDetailConfirm"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import QuotationDetail from '@/components/QuotationDetail/index.vue'
import ProjectQuotationItemImport from './ProjectQuotationItemImport.vue'
import { defineProps, defineEmits } from 'vue'

const quotationDetailData = ref([])

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    console.log('modelValue 发生变化:', {
      oldValue,
      newValue
    })
    quotationDetailData.value = newValue
    quotationDetailState.value = convertToCurrentState(newValue)
  },
  { deep: true }
)

const emit = defineEmits(['update:modelValue', 'filter-change'])

// 筛选条件
const filters = ref({})

// 计算唯一值
const getUniqueValues = (prop) => {
  const uniqueValues = [...new Set(quotationDetailData.value.map(item => item[prop]))]
  return uniqueValues.filter(value => value !== undefined).map(value => ({ text: value, value }))
}

// 处理筛选变化
const handleFilterChange = (filters) => {
  emit('filter-change', filters)
}

// 是否显示检测明细项目配置
const quotationDetailOpen = ref(false)
// 检测明细项目组件的内部状态数据
const quotationDetailState = ref({
  categories: [],
  testItemsData: {},
  samplingPointsData: {}
})

// 打开检测明细项目配置
function openQuotationDetail() {
  quotationDetailOpen.value = true
}

// 转换函数，将扁平化数据转换为 currentState 格式
function convertToCurrentState(flattenedData) {
  console.log('转换前的扁平化数据:', flattenedData)
  const categories = new Set()
  const testItemsData = {}
  const samplingPointsData = {}

  flattenedData.forEach(item => {
    categories.add(item.category)
    
    if (!testItemsData[item.category]) {
      testItemsData[item.category] = []
    }
    
    let testItem = testItemsData[item.category].find(ti => ti.id === item.testItemId)
    if (!testItem) {
      testItem = {
        id: item.testItemId,
        classification: item.classification,
        parameter: item.parameter,
        method: item.method,
        limitationScope: item.limitationScope,
        remark: item.remark
      }
      testItemsData[item.category].push(testItem)
    }
    
    if (item.pointName) {
      const key = `${item.category}_${item.testItemId}`
      if (!samplingPointsData[key]) {
        samplingPointsData[key] = []
      }
      
      samplingPointsData[key].push({
        pointName: item.pointName,
        pointCount: item.pointCount,
        cycleType: item.cycleType,
        cycleCount: item.cycleCount,
        frequency: item.frequency,
        sampleCount: item.sampleCount,
        sampleSource: item.sampleSource,
        isSubcontract: item.isSubcontract
      })
    }
  })

  return {
    categories: Array.from(categories),
    testItemsData,
    samplingPointsData
  }
}

// 处理检测明细项目配置确认
function handleQuotationDetailConfirm(data, componentState) {
  // 保存组件的内部状态，以便下次打开时恢复
  if (componentState) {
    quotationDetailState.value = {
      categories: componentState.categories || [],
      testItemsData: componentState.testItemsData || {},
      samplingPointsData: componentState.samplingPointsData || {}
    }
  }
  quotationDetailOpen.value = false
  ElMessage.success('检测明细项目配置成功')
  quotationDetailData.value = data
  console.log('检测明细项目配置:', data)
  emit('update:modelValue', data)
}

// 处理批量导入成功
function handleImportSuccess(importedData) {
  console.log('批量导入成功:', importedData)

  // 将导入的数据添加到现有数据中
  const newData = [...quotationDetailData.value, ...importedData]
  quotationDetailData.value = newData

  // 更新组件状态
  quotationDetailState.value = convertToCurrentState(newData)

  // 通知父组件
  emit('update:modelValue', newData)

  ElMessage.success(`成功导入 ${importedData.length} 条检测明细项目`)
}

// 在 <script setup> 中添加过滤方法
const filterByValue = (value, row, column) => {
  const property = column['property']
  return row[property] === value
}
</script>

<style scoped>
.fee-summary {
  margin-top: 10px;
  text-align: right;
}
.empty-state {
  margin-top: 20px;
  text-align: center;
}
</style>