import request from '@/utils/request';

// 获取技术手册分页列表
export function getTechnicalManualPage(query) {
  return request({
    url: '/basedata/technical-manual/page',
    method: 'get',
    params: query
  });
}

// 获取技术手册列表
export function getTechnicalManualList(query) {
  return request({
    url: '/basedata/technical-manual/list',
    method: 'get',
    params: query
  });
}

// 获取技术手册详情
export function getTechnicalManualDetail(id) {
  return request({
    url: `/basedata/technical-manual/${id}`,
    method: 'get'
  });
}

// 新增技术手册
export function addTechnicalManual(data) {
  return request({
    url: '/basedata/technical-manual',
    method: 'post',
    data: data
  });
}

// 修改技术手册
export function editTechnicalManual(data) {
  return request({
    url: '/basedata/technical-manual',
    method: 'put',
    data: data
  });
}

// 删除技术手册
export function deleteTechnicalManual(id) {
  return request({
    url: `/basedata/technical-manual/${id}`,
    method: 'delete'
  });
}

// 获取检测类别
export function getCategories() {
  return request({
    url: '/basedata/technical-manual/options/categories',
    method: 'get'
  });
}

// 获取检测参数
export function getParameters(category) {
  return request({
    url: '/basedata/technical-manual/options/parameters',
    method: 'get',
    params: { category }
  });
}

// 获取检测方法
export function getMethods(category, parameter) {
  return request({
    url: '/basedata/technical-manual/options/methods',
    method: 'get',
    params: { category, parameter }
  });
}
