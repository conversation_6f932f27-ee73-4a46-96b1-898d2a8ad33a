<template>
  <el-dialog :title="title" v-model="dialogVisible" width="800px" append-to-body>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="basic">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客户级别">
            {{ form.customerLevel === '1' ? '集团' : '公司' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属集团" v-if="form.customerLevel === '2'">
            {{ form.parent?.customerName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ form.customerName }}
          </el-descriptions-item>
          <el-descriptions-item label="客户类型">
            {{ form.customerType }}
          </el-descriptions-item>
          <el-descriptions-item label="客户来源">
            {{ form.customerSource }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="form.status === '0' ? 'success' : 'danger'">
              {{ form.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="所在地区" :span="2">
            {{ form.province }} {{ form.city }} {{ form.district }}
          </el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">
            {{ form.address }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ form.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </el-tab-pane>
      <el-tab-pane label="联系人信息" name="contacts">
        <el-table :data="form.contacts" style="margin-top: 10px;">
          <el-table-column label="联系人姓名" align="center" prop="contactName" />
          <el-table-column label="职务" align="center" prop="position" />
          <el-table-column label="电话" align="center" prop="phone" />
          <el-table-column label="微信" align="center" prop="wechat" />
          <el-table-column label="邮箱" align="center" prop="email" />
          <el-table-column label="主要联系人" align="center" prop="isPrimary" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isPrimary === '1' ? 'success' : 'info'">
                {{ scope.row.isPrimary === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="内部负责人" name="managers">
        <el-table :data="form.internalManagers" style="margin-top: 10px;">
          <el-table-column label="负责人" align="center">
            <template #default="scope">
              {{ scope.row.user?.nickName || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="主要负责人" align="center" prop="isPrimary" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.isPrimary === '1' ? 'success' : 'info'">
                {{ scope.row.isPrimary === '1' ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: "客户详情"
  },
  open: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["cancel"]);
const { proxy } = getCurrentInstance();

const dialogVisible = computed({
  get: () => props.open,
  set: (val) => {
    if (!val) {
      emit("cancel");
    }
  }
});

const activeName = ref("basic");
const form = ref({
  customerId: undefined,
  customerName: undefined,
  customerLevel: undefined,
  parentId: undefined,
  customerType: undefined,
  customerSource: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  address: undefined,
  status: undefined,
  remark: undefined,
  contacts: [],
  internalManagers: [],
  parent: undefined
});

// 设置表单数据
function setFormData(data) {
  console.log("设置详情数据:", JSON.stringify(data));
  // 深拷贝数据，避免引用问题
  const newData = JSON.parse(JSON.stringify(data));
  // 设置表单数据
  form.value = newData;
  console.log("设置后的详情数据:", JSON.stringify(form.value));
}

// 取消按钮
function cancel() {
  emit("cancel");
}

// 对外暴露方法
defineExpose({
  setFormData,
  form
});
</script>
