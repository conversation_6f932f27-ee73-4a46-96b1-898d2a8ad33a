<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>编辑报价单</span>
      </div>

      <!-- 步骤条 -->
      <el-steps :active="active" finish-status="success" align-center style="margin-bottom: 30px;">
        <el-step title="报价基本信息"></el-step>
        <el-step title="报价明细信息"></el-step>
      </el-steps>

      <!-- 表单内容 -->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" v-loading="loading">
        <!-- 步骤一：报价基本信息 -->
        <div v-show="active === 0">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报价编号" prop="quotationNo">
                <el-input v-model="form.quotationNo" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态" prop="status">
                <el-tag :type="statusTypeMap[form.status]">{{ statusLabelMap[form.status] }}</el-tag>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户名称" prop="customerName">
                <el-autocomplete
                  v-model="form.customerName"
                  :fetch-suggestions="searchCustomers"
                  placeholder="请输入客户名称"
                  style="width: 100%"
                  @select="handleCustomerSelect"
                ></el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="客户地址" prop="customerAddress">
                <el-input v-model="form.customerAddress" placeholder="请输入客户地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系人" prop="contactName">
                <el-input v-model="form.contactName" placeholder="请输入联系人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="contactPhone">
                <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="受检方" prop="inspectedParty">
                <el-autocomplete
                  v-model="form.inspectedParty"
                  :fetch-suggestions="searchCustomers"
                  placeholder="请输入受检方"
                  style="width: 100%"
                  @select="handleInspectedPartySelect"
                ></el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受检方地址" prop="inspectedAddress">
                <el-input v-model="form.inspectedAddress" placeholder="请输入受检方地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="受检方联系人" prop="inspectedContact">
                <el-input v-model="form.inspectedContact" placeholder="请输入受检方联系人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受检方电话" prop="inspectedPhone">
                <el-input v-model="form.inspectedPhone" placeholder="请输入受检方电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 步骤二：报价明细信息 -->
        <div v-show="active === 1">
          <!-- 报价项目 -->
          <el-card class="box-card" shadow="never">
            <div slot="header" class="clearfix">
              <span>报价项目</span>
              <el-button style="float: right; padding: 3px 0" type="text" @click="handleAddItem">新增项目</el-button>
            </div>

            <!-- 批量操作 -->
            <el-row :gutter="20" style="margin-bottom: 15px;">
              <el-col :span="24">
                <el-button type="primary" size="small" @click="batchEditDialogVisible = true" :disabled="selectedItems.length === 0">批量修改</el-button>
                <el-button type="primary" size="small" @click="batchAddDialogVisible = true">批量录入</el-button>
              </el-col>
            </el-row>

            <!-- 搜索框 -->
            <el-row :gutter="20" style="margin-bottom: 15px;">
              <el-col :span="6">
                <el-input
                  placeholder="请输入检测参数搜索"
                  v-model="itemSearchKeyword"
                  clearable
                  @keyup.enter.native="filterItems"
                >
                  <el-button slot="append" icon="el-icon-search" @click="filterItems"></el-button>
                </el-input>
              </el-col>
            </el-row>

            <!-- 项目表格 -->
            <el-table
              :data="filteredItems"
              border
              style="width: 100%"
              @selection-change="handleItemSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="检测参数" prop="parameter">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.parameter" placeholder="请输入检测参数"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="检测方法" prop="method">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.method" placeholder="请输入检测方法"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="点位数" prop="pointCount" width="100">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.pointCount" :min="1" :precision="0" @change="calculateItemAmount(scope.row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="周期类型" prop="cycleType" width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.cycleType" placeholder="请输入周期类型"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="周期数" prop="cycleCount" width="100">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.cycleCount" :min="1" :precision="0" @change="calculateItemAmount(scope.row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="频次数" prop="frequency" width="100">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.frequency" :min="1" :precision="0" @change="calculateItemAmount(scope.row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="样品数" prop="sampleCount" width="100">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.sampleCount" :min="1" :precision="0" @change="calculateItemAmount(scope.row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="单价" prop="unitPrice" width="120">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.unitPrice" :min="0" :precision="2" @change="calculateItemAmount(scope.row)"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="金额" prop="amount" width="120">
                <template slot-scope="scope">
                  <el-input-number v-model="scope.row.amount" :min="0" :precision="2" :disabled="true"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-delete"
                    @click="removeItem(scope.$index)"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 总金额 -->
            <div style="margin-top: 15px; text-align: right;">
              <span style="font-weight: bold;">总金额：{{ form.totalAmount }} 元</span>
            </div>
          </el-card>

          <!-- 附件上传 -->
          <el-card class="box-card" shadow="never" style="margin-top: 20px;">
            <div slot="header" class="clearfix">
              <span>附件上传</span>
            </div>
            <el-upload
              class="upload-demo"
              action="#"
              :http-request="uploadFile"
              :file-list="fileList"
              :on-remove="handleRemoveFile"
              :before-upload="beforeUploadFile"
              multiple
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">可上传任意类型文件作为报价单补充材料</div>
            </el-upload>
          </el-card>
        </div>

        <!-- 表单操作按钮 -->
        <div style="margin-top: 20px; text-align: center;">
          <el-button @click="prev" v-if="active > 0">上一步</el-button>
          <el-button type="primary" @click="next" v-if="active < 1">下一步</el-button>
          <el-button type="primary" @click="submitForm" v-if="active === 1">保存</el-button>
          <el-button type="success" @click="submitAndAudit" v-if="active === 1 && (form.status === '0' || form.status === '3' || form.status === '4')">保存并提交审核</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </el-form>
    </el-card>

    <!-- 批量修改对话框 -->
    <el-dialog title="批量修改" :visible.sync="batchEditDialogVisible" width="500px" append-to-body>
      <el-form ref="batchEditForm" :model="batchEditForm" label-width="100px">
        <el-form-item label="点位数">
          <el-input-number v-model="batchEditForm.pointCount" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="周期类型">
          <el-input v-model="batchEditForm.cycleType" placeholder="请输入周期类型"></el-input>
        </el-form-item>
        <el-form-item label="周期数">
          <el-input-number v-model="batchEditForm.cycleCount" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="频次数">
          <el-input-number v-model="batchEditForm.frequency" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="样品数">
          <el-input-number v-model="batchEditForm.sampleCount" :min="1" :precision="0"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchEdit">确 定</el-button>
        <el-button @click="batchEditDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 批量录入对话框 -->
    <el-dialog title="批量录入参数" :visible.sync="batchAddDialogVisible" width="500px" append-to-body>
      <el-form ref="batchAddForm" :model="batchAddForm" label-width="100px">
        <el-form-item label="检测参数">
          <el-input
            type="textarea"
            v-model="batchAddForm.parameters"
            placeholder="请输入检测参数，多个参数用分号、顿号或回车分隔"
            :rows="5"
          ></el-input>
        </el-form-item>
        <el-form-item label="检测方法">
          <el-input v-model="batchAddForm.method" placeholder="请输入检测方法"></el-input>
        </el-form-item>
        <el-form-item label="点位数">
          <el-input-number v-model="batchAddForm.pointCount" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="周期类型">
          <el-input v-model="batchAddForm.cycleType" placeholder="请输入周期类型"></el-input>
        </el-form-item>
        <el-form-item label="周期数">
          <el-input-number v-model="batchAddForm.cycleCount" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="频次数">
          <el-input-number v-model="batchAddForm.frequency" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="样品数">
          <el-input-number v-model="batchAddForm.sampleCount" :min="1" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="单价">
          <el-input-number v-model="batchAddForm.unitPrice" :min="0" :precision="2"></el-input-number>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmBatchAdd">确 定</el-button>
        <el-button @click="batchAddDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getQuotationDetail, editQuotation, submitQuotation, searchCustomers, uploadFile } from "@/api/quotation/quotation";

export default {
  name: "QuotationEdit",
  data() {
    return {
      // 当前步骤
      active: 0,
      // 加载状态
      loading: false,
      // 表单参数
      form: {
        id: undefined,
        quotationNo: "",
        customerId: undefined,
        customerName: "",
        customerAddress: "",
        contactName: "",
        contactPhone: "",
        inspectedParty: "",
        inspectedContact: "",
        inspectedPhone: "",
        inspectedAddress: "",
        totalAmount: 0,
        status: "0",
        remark: "",
        items: [],
        attachments: []
      },
      // 表单校验规则
      rules: {
        customerName: [
          { required: true, message: "客户名称不能为空", trigger: "blur" }
        ],
        inspectedParty: [
          { required: true, message: "受检方不能为空", trigger: "blur" }
        ]
      },
      // 状态类型映射
      statusTypeMap: {
        "0": "info",
        "1": "warning",
        "2": "success",
        "3": "info",
        "4": "danger"
      },
      // 状态标签映射
      statusLabelMap: {
        "0": "草稿",
        "1": "待审核",
        "2": "已审核",
        "3": "已撤回",
        "4": "已拒绝"
      },
      // 项目搜索关键字
      itemSearchKeyword: "",
      // 已选择的项目
      selectedItems: [],
      // 批量修改表单
      batchEditForm: {
        pointCount: 1,
        cycleType: "",
        cycleCount: 1,
        frequency: 1,
        sampleCount: 1
      },
      // 批量修改对话框是否显示
      batchEditDialogVisible: false,
      // 批量录入表单
      batchAddForm: {
        parameters: "",
        method: "",
        pointCount: 1,
        cycleType: "",
        cycleCount: 1,
        frequency: 1,
        sampleCount: 1,
        unitPrice: 0
      },
      // 批量录入对话框是否显示
      batchAddDialogVisible: false,
      // 文件列表
      fileList: []
    };
  },
  computed: {
    // 过滤后的项目列表
    filteredItems() {
      if (!this.itemSearchKeyword) {
        return this.form.items;
      }
      return this.form.items.filter(item =>
        item.parameter.toLowerCase().includes(this.itemSearchKeyword.toLowerCase())
      );
    }
  },
  created() {
    const id = this.$route.params && this.$route.params.id;
    if (id) {
      this.getDetail(id);
    }
  },
  methods: {
    /** 获取报价单详情 */
    getDetail(id) {
      this.loading = true;
      getQuotationDetail(id).then(response => {
        this.form = response.data;

        // 处理附件列表
        this.fileList = this.form.attachments.map(item => {
          return {
            name: item.fileName,
            url: item.filePath
          };
        });

        this.loading = false;
      });
    },
    /** 搜索客户 */
    searchCustomers(queryString, cb) {
      if (queryString.length < 2) {
        cb([]);
        return;
      }
      searchCustomers(queryString).then(response => {
        const customers = response.data.map(item => {
          return {
            value: item.name,
            id: item.id,
            address: item.address,
            contactName: item.contactName,
            contactPhone: item.contactPhone
          };
        });
        cb(customers);
      });
    },
    /** 选择客户 */
    handleCustomerSelect(item) {
      this.form.customerId = item.id;
      this.form.customerName = item.value;
      this.form.customerAddress = item.address || "";
      this.form.contactName = item.contactName || "";
      this.form.contactPhone = item.contactPhone || "";
    },
    /** 选择受检方 */
    handleInspectedPartySelect(item) {
      this.form.inspectedParty = item.value;
      this.form.inspectedAddress = item.address || "";
      this.form.inspectedContact = item.contactName || "";
      this.form.inspectedPhone = item.contactPhone || "";
    },
    /** 上一步 */
    prev() {
      if (this.active > 0) {
        this.active--;
      }
    },
    /** 下一步 */
    next() {
      if (this.active === 0) {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.active++;
          }
        });
      } else {
        this.active++;
      }
    },
    /** 添加项目 */
    handleAddItem() {
      this.form.items.push({
        parameter: "",
        method: "",
        pointCount: 1,
        cycleType: "",
        cycleCount: 1,
        frequency: 1,
        sampleCount: 1,
        unitPrice: 0,
        amount: 0
      });
    },
    /** 移除项目 */
    removeItem(index) {
      this.form.items.splice(index, 1);
      this.calculateTotalAmount();
    },
    /** 计算项目金额 */
    calculateItemAmount(item) {
      if (item) {
        item.amount = item.unitPrice * item.pointCount * item.cycleCount * item.frequency * item.sampleCount;
        this.calculateTotalAmount();
      }
    },
    /** 计算总金额 */
    calculateTotalAmount() {
      this.form.totalAmount = this.form.items.reduce((sum, item) => sum + (item.amount || 0), 0);
    },
    /** 项目表格选择变更 */
    handleItemSelectionChange(selection) {
      this.selectedItems = selection;
    },
    /** 确认批量修改 */
    confirmBatchEdit() {
      this.selectedItems.forEach(item => {
        if (this.batchEditForm.pointCount) item.pointCount = this.batchEditForm.pointCount;
        if (this.batchEditForm.cycleType) item.cycleType = this.batchEditForm.cycleType;
        if (this.batchEditForm.cycleCount) item.cycleCount = this.batchEditForm.cycleCount;
        if (this.batchEditForm.frequency) item.frequency = this.batchEditForm.frequency;
        if (this.batchEditForm.sampleCount) item.sampleCount = this.batchEditForm.sampleCount;
        this.calculateItemAmount(item);
      });
      this.batchEditDialogVisible = false;
    },
    /** 确认批量录入 */
    confirmBatchAdd() {
      // 分割参数
      const parameters = this.batchAddForm.parameters.split(/[;\n、]/);

      // 添加项目
      parameters.forEach(parameter => {
        parameter = parameter.trim();
        if (parameter) {
          const item = {
            parameter: parameter,
            method: this.batchAddForm.method,
            pointCount: this.batchAddForm.pointCount,
            cycleType: this.batchAddForm.cycleType,
            cycleCount: this.batchAddForm.cycleCount,
            frequency: this.batchAddForm.frequency,
            sampleCount: this.batchAddForm.sampleCount,
            unitPrice: this.batchAddForm.unitPrice,
            amount: this.batchAddForm.unitPrice * this.batchAddForm.pointCount * this.batchAddForm.cycleCount * this.batchAddForm.frequency * this.batchAddForm.sampleCount
          };
          this.form.items.push(item);
        }
      });

      this.calculateTotalAmount();
      this.batchAddDialogVisible = false;
    },
    /** 过滤项目 */
    filterItems() {
      // 已在计算属性中实现
    },
    /** 上传文件前的钩子 */
    beforeUploadFile(file) {
      return true;
    },
    /** 自定义上传 */
    uploadFile(options) {
      const { file } = options;
      uploadFile(file).then(response => {
        const attachment = response.data;
        this.form.attachments.push(attachment);
        this.fileList.push({
          name: attachment.fileName,
          url: attachment.filePath
        });
        this.$message.success("上传成功");
      }).catch(error => {
        this.$message.error("上传失败");
      });
    },
    /** 移除文件 */
    handleRemoveFile(file, fileList) {
      const index = this.form.attachments.findIndex(item => item.fileName === file.name);
      if (index !== -1) {
        this.form.attachments.splice(index, 1);
      }
      this.fileList = fileList;
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 校验项目
          if (this.form.items.length === 0) {
            this.$message.error("请至少添加一个报价项目");
            return;
          }

          // 校验项目数据完整性
          for (let i = 0; i < this.form.items.length; i++) {
            const item = this.form.items[i];
            if (!item.parameter || !item.method) {
              this.$message.error(`第${i+1}行报价项目的检测参数和检测方法不能为空`);
              return;
            }
          }

          // 提交表单
          editQuotation(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.$router.push({ path: "/quotation" });
          });
        }
      });
    },
    /** 保存并提交审核 */
    submitAndAudit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 校验项目
          if (this.form.items.length === 0) {
            this.$message.error("请至少添加一个报价项目");
            return;
          }

          // 校验项目数据完整性
          for (let i = 0; i < this.form.items.length; i++) {
            const item = this.form.items[i];
            if (!item.parameter || !item.method) {
              this.$message.error(`第${i+1}行报价项目的检测参数和检测方法不能为空`);
              return;
            }
          }

          // 提交表单
          editQuotation(this.form).then(response => {
            // 提交审核
            submitQuotation({
              id: this.form.id,
              remark: "修改并提交审核"
            }).then(() => {
              this.$modal.msgSuccess("修改并提交审核成功");
              this.$router.push({ path: "/quotation" });
            });
          });
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.$router.push({ path: "/quotation" });
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
