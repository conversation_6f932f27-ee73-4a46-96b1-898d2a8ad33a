"""
测试项目报价费用计算功能
"""

import asyncio
from decimal import Decimal
from datetime import datetime
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 模拟数据
test_project_quotation_items = [
    {
        "category": "环境空气和废气",
        "method": "环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法 HJ 645-2013",
        "parameter": "1,1-二氯乙烷",
        "point_count": 2,
        "cycle_count": 1,
        "frequency": 1,
        "sample_count": 1
    },
    {
        "category": "环境空气和废气", 
        "method": "环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法 HJ 645-2013",
        "parameter": "1,2-二氯乙烷",
        "point_count": 2,
        "cycle_count": 1,
        "frequency": 1,
        "sample_count": 1
    },
    {
        "category": "水和废水",
        "method": "玻璃电极法 GB 6920-86",
        "parameter": "pH值",
        "point_count": 3,
        "cycle_count": 4,
        "frequency": 2,
        "sample_count": 1
    }
]

test_technical_manual_prices = [
    {
        "category": "环境空气和废气",
        "method": "环境空气 挥发性卤代烃的测定 活性炭吸附-二硫化碳解吸/气相色谱法 HJ 645-2013",
        "classification": "气",
        "first_item_price": Decimal("120.00"),
        "additional_item_price": Decimal("60.00"),
        "testing_fee_limit": Decimal("600.00"),
        "sampling_price": Decimal("50.00"),
        "pretreatment_price": Decimal("30.00"),
        "special_consumables_price": Decimal("20.00")
    },
    {
        "category": "水和废水",
        "method": "玻璃电极法 GB 6920-86",
        "classification": "水",
        "first_item_price": Decimal("80.00"),
        "additional_item_price": Decimal("0.00"),
        "testing_fee_limit": Decimal("80.00"),
        "sampling_price": Decimal("40.00"),
        "pretreatment_price": Decimal("20.00"),
        "special_consumables_price": Decimal("10.00")
    }
]

def test_fee_calculation_logic():
    """
    测试费用计算逻辑
    """
    print("=== 项目报价费用计算测试 ===\n")
    
    # 按类别-方法分组
    category_method_groups = {}
    for item in test_project_quotation_items:
        key = f"{item['category']}_{item['method']}"
        if key not in category_method_groups:
            category_method_groups[key] = []
        category_method_groups[key].append(item)
    
    # 创建价格映射
    price_map = {}
    for price in test_technical_manual_prices:
        key = f"{price['category']}_{price['method']}"
        price_map[key] = price
    
    total_sampling_fee = Decimal("0")
    total_testing_fee = Decimal("0")
    total_pretreatment_fee = Decimal("0")
    
    print("费用计算详情：\n")
    
    for key, items in category_method_groups.items():
        if key not in price_map:
            print(f"❌ 未找到价格信息: {key}")
            continue
            
        price_info = price_map[key]
        print(f"📋 类别-方法组: {price_info['category']} - {price_info['method'][:50]}...")
        
        # 计算参数数量
        unique_parameters = set(item['parameter'] for item in items)
        parameter_count = len(unique_parameters)
        print(f"   参数数量: {parameter_count} ({', '.join(unique_parameters)})")
        
        # 计算检测单价
        if parameter_count == 1:
            testing_unit_price = price_info['first_item_price']
            print(f"   检测单价: {testing_unit_price} (首项单价)")
        else:
            testing_unit_price = price_info['first_item_price'] + (parameter_count - 1) * price_info['additional_item_price']
            testing_unit_price = min(testing_unit_price, price_info['testing_fee_limit'])
            print(f"   检测单价: {testing_unit_price} (首项 + {parameter_count-1} × 增项，限制: {price_info['testing_fee_limit']})")
        
        group_sampling_fee = Decimal("0")
        group_testing_fee = Decimal("0")
        group_pretreatment_fee = Decimal("0")
        
        for item in items:
            point_count = item['point_count']
            cycle_count = item['cycle_count']
            frequency = item['frequency']
            sample_count = item['sample_count']
            
            # 采样费用 = 采样单价 × 点位数 × 监测周期 × 监测频率
            sampling_fee = price_info['sampling_price'] * point_count * cycle_count * frequency
            
            # 检测费用 = 检测单价 × 点位数 × 监测周期 × 监测频率 × 样品数
            testing_fee = testing_unit_price * point_count * cycle_count * frequency * sample_count
            
            # 前处理费用 = 前处理单价 × 点位数 × 监测周期 × 监测频率
            pretreatment_fee = price_info['pretreatment_price'] * point_count * cycle_count * frequency
            
            group_sampling_fee += sampling_fee
            group_testing_fee += testing_fee
            group_pretreatment_fee += pretreatment_fee
            
            print(f"   📊 {item['parameter']}:")
            print(f"      点位数: {point_count}, 周期: {cycle_count}, 频率: {frequency}, 样品数: {sample_count}")
            print(f"      采样费: {sampling_fee}, 检测费: {testing_fee}, 前处理费: {pretreatment_fee}")
        
        total_sampling_fee += group_sampling_fee
        total_testing_fee += group_testing_fee
        total_pretreatment_fee += group_pretreatment_fee
        
        print(f"   💰 组合计: 采样 {group_sampling_fee}, 检测 {group_testing_fee}, 前处理 {group_pretreatment_fee}")
        print()
    
    total_fee = total_sampling_fee + total_testing_fee + total_pretreatment_fee
    
    print("=== 费用汇总 ===")
    print(f"总采样费用: {total_sampling_fee}")
    print(f"总检测费用: {total_testing_fee}")
    print(f"总前处理费用: {total_pretreatment_fee}")
    print(f"项目总费用: {total_fee}")
    
    return {
        "sampling_fee": total_sampling_fee,
        "testing_fee": total_testing_fee,
        "pretreatment_fee": total_pretreatment_fee,
        "total_fee": total_fee
    }

def test_edge_cases():
    """
    测试边界情况
    """
    print("\n=== 边界情况测试 ===\n")
    
    # 测试单参数情况
    print("1. 单参数检测费用计算:")
    first_price = Decimal("100")
    additional_price = Decimal("50")
    limit = Decimal("500")
    
    for param_count in [1, 2, 5, 10]:
        if param_count == 1:
            unit_price = first_price
        else:
            unit_price = first_price + (param_count - 1) * additional_price
            unit_price = min(unit_price, limit)
        print(f"   参数数量 {param_count}: 检测单价 {unit_price}")
    
    # 测试费用上限
    print("\n2. 费用上限测试:")
    first_price = Decimal("100")
    additional_price = Decimal("80")
    limit = Decimal("300")
    
    for param_count in [1, 2, 3, 4, 5]:
        if param_count == 1:
            unit_price = first_price
        else:
            unit_price = first_price + (param_count - 1) * additional_price
            original_price = unit_price
            unit_price = min(unit_price, limit)
        print(f"   参数数量 {param_count}: 计算价格 {original_price if param_count > 1 else first_price}, 实际价格 {unit_price}")

if __name__ == "__main__":
    # 运行费用计算测试
    result = test_fee_calculation_logic()
    
    # 运行边界情况测试
    test_edge_cases()
    
    print("\n=== 测试完成 ===")
    print("费用计算逻辑验证通过！")
