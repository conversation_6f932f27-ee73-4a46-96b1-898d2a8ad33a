<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 查询条件 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="报价编号" prop="quotationNo">
          <el-input
            v-model="queryParams.quotationNo"
            placeholder="请输入报价编号"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input
            v-model="queryParams.customerName"
            placeholder="请输入客户名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="dateRange"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            @click="handleAdd"
            v-hasPermi="['quotation:quotation:add']"
          >新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            :disabled="single"
            @click="handleEdit"
            v-hasPermi="['quotation:quotation:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['quotation:quotation:remove']"
          >删除</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="quotationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="报价编号" align="center" prop="quotationNo" />
        <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
        <el-table-column label="联系人" align="center" prop="contactName" />
        <el-table-column label="联系电话" align="center" prop="contactPhone" />
        <el-table-column label="总金额" align="center" prop="totalAmount" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="statusTypeMap[scope.row.status]">{{ statusLabelMap[scope.row.status] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['quotation:quotation:query']"
            >查看</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
              v-hasPermi="['quotation:quotation:edit']"
              v-if="scope.row.status === '0' || scope.row.status === '3' || scope.row.status === '4'"
            >修改</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['quotation:quotation:remove']"
              v-if="scope.row.status === '0' || scope.row.status === '3' || scope.row.status === '4'"
            >删除</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-promotion"
              @click="handleSubmit(scope.row)"
              v-hasPermi="['quotation:quotation:submit']"
              v-if="scope.row.status === '0' || scope.row.status === '3' || scope.row.status === '4'"
            >提交</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-check"
              @click="handleAudit(scope.row)"
              v-hasPermi="['quotation:quotation:audit']"
              v-if="scope.row.status === '1'"
            >审核</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-back"
              @click="handleWithdraw(scope.row)"
              v-hasPermi="['quotation:quotation:withdraw']"
              v-if="scope.row.status === '1'"
            >撤回</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 提交对话框 -->
    <el-dialog title="提交报价单" :visible.sync="submitOpen" width="500px" append-to-body>
      <el-form ref="submitForm" :model="submitForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="submitForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitQuotation">确 定</el-button>
        <el-button @click="submitOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog title="审核报价单" :visible.sync="auditOpen" width="500px" append-to-body>
      <el-form ref="auditForm" :model="auditForm" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio label="2">通过</el-radio>
            <el-radio label="4">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="auditForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="auditQuotation">确 定</el-button>
        <el-button @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 撤回对话框 -->
    <el-dialog title="撤回报价单" :visible.sync="withdrawOpen" width="500px" append-to-body>
      <el-form ref="withdrawForm" :model="withdrawForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="withdrawForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="withdrawQuotation">确 定</el-button>
        <el-button @click="withdrawOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getQuotationPage, 
  deleteQuotation, 
  submitQuotation, 
  auditQuotation, 
  withdrawQuotation 
} from "@/api/quotation/quotation";

export default {
  name: "Quotation",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报价单表格数据
      quotationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 状态选项
      statusOptions: [
        { value: "0", label: "草稿" },
        { value: "1", label: "待审核" },
        { value: "2", label: "已审核" },
        { value: "3", label: "已撤回" },
        { value: "4", label: "已拒绝" }
      ],
      // 状态类型映射
      statusTypeMap: {
        "0": "info",
        "1": "warning",
        "2": "success",
        "3": "info",
        "4": "danger"
      },
      // 状态标签映射
      statusLabelMap: {
        "0": "草稿",
        "1": "待审核",
        "2": "已审核",
        "3": "已撤回",
        "4": "已拒绝"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        quotationNo: undefined,
        customerName: undefined,
        status: undefined,
        beginTime: undefined,
        endTime: undefined
      },
      // 提交表单参数
      submitForm: {
        id: undefined,
        remark: undefined
      },
      // 提交对话框是否显示
      submitOpen: false,
      // 审核表单参数
      auditForm: {
        id: undefined,
        status: "2",
        remark: undefined
      },
      // 审核对话框是否显示
      auditOpen: false,
      // 撤回表单参数
      withdrawForm: {
        id: undefined,
        remark: undefined
      },
      // 撤回对话框是否显示
      withdrawOpen: false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询报价单列表 */
    getList() {
      this.loading = true;
      // 处理日期范围
      this.queryParams.beginTime = this.dateRange && this.dateRange.length > 0 ? this.dateRange[0] : undefined;
      this.queryParams.endTime = this.dateRange && this.dateRange.length > 0 ? this.dateRange[1] : undefined;
      
      getQuotationPage(this.queryParams).then(response => {
        this.quotationList = response.data.items;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        quotationNo: undefined,
        customerId: undefined,
        customerName: undefined,
        customerAddress: undefined,
        contactName: undefined,
        contactPhone: undefined,
        inspectedParty: undefined,
        inspectedContact: undefined,
        inspectedPhone: undefined,
        inspectedAddress: undefined,
        totalAmount: 0,
        status: "0",
        remark: undefined,
        items: [],
        attachments: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push({ path: "/quotation/add" });
    },
    /** 修改按钮操作 */
    handleEdit(row) {
      const id = row.id || this.ids[0];
      this.$router.push({ path: `/quotation/edit/${id}` });
    },
    /** 查看按钮操作 */
    handleView(row) {
      const id = row.id || this.ids[0];
      this.$router.push({ path: `/quotation/detail/${id}` });
    },
    /** 提交按钮操作 */
    handleSubmit(row) {
      this.submitForm = {
        id: row.id,
        remark: undefined
      };
      this.submitOpen = true;
    },
    /** 提交报价单 */
    submitQuotation() {
      submitQuotation(this.submitForm).then(response => {
        this.$modal.msgSuccess("提交成功");
        this.submitOpen = false;
        this.getList();
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: "2",
        remark: undefined
      };
      this.auditOpen = true;
    },
    /** 审核报价单 */
    auditQuotation() {
      auditQuotation(this.auditForm).then(response => {
        this.$modal.msgSuccess("审核成功");
        this.auditOpen = false;
        this.getList();
      });
    },
    /** 撤回按钮操作 */
    handleWithdraw(row) {
      this.withdrawForm = {
        id: row.id,
        remark: undefined
      };
      this.withdrawOpen = true;
    },
    /** 撤回报价单 */
    withdrawQuotation() {
      withdrawQuotation(this.withdrawForm).then(response => {
        this.$modal.msgSuccess("撤回成功");
        this.withdrawOpen = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除报价单编号为"' + (row.quotationNo || '') + '"的数据项？').then(function() {
        return deleteQuotation(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
