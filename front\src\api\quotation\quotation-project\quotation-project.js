import request from '@/utils/request';

// 获取报价单分页列表
export function getQuotationPage(query) {
  return request({
    url: '/quotation/page',
    method: 'get',
    params: query
  });
}

// 获取报价单列表
export function getQuotationList(query) {
  return request({
    url: '/quotation/list',
    method: 'get',
    params: query
  });
}

// 获取报价单详情
export function getQuotationDetail(id) {
  return request({
    url: `/quotation/${id}`,
    method: 'get'
  });
}

// 新增报价单
export function addQuotation(data) {
  return request({
    url: '/quotation',
    method: 'post',
    data: data
  });
}

// 修改报价单
export function editQuotation(data) {
  return request({
    url: '/quotation',
    method: 'put',
    data: data
  });
}

// 删除报价单
export function deleteQuotation(id) {
  return request({
    url: `/quotation/${id}`,
    method: 'delete'
  });
}

// 提交报价单
export function submitQuotation(data) {
  return request({
    url: '/quotation/submit',
    method: 'post',
    data: data
  });
}

// 审核报价单
export function auditQuotation(data) {
  return request({
    url: '/quotation/audit',
    method: 'post',
    data: data
  });
}

// 撤回报价单
export function withdrawQuotation(data) {
  return request({
    url: '/quotation/withdraw',
    method: 'post',
    data: data
  });
}

// 获取操作日志列表
export function getOperationLogs(quotationId) {
  return request({
    url: `/quotation/logs/${quotationId}`,
    method: 'get'
  });
}

// 搜索客户
export function searchCustomers(keyword) {
  return request({
    url: '/quotation/customer/search',
    method: 'get',
    params: { keyword }
  });
}

// 上传附件
export function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/quotation/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
