from datetime import datetime

from fastapi import Request, Response
from sqlalchemy import and_, or_, select, distinct, func
from sqlalchemy.ext.asyncio import AsyncSession

from config.base_service import BaseService
from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_basedata.entity.do.technical_manual_price_do import TechnicalManualPrice
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_basedata.entity.vo.technical_manual_price_vo import (
    AddTechnicalManualPriceModel,
    EditTechnicalManualPriceModel,
    TechnicalManualPricePageQueryModel,
    TechnicalManualPriceQueryModel,
    MethodCategoryOptionModel,
)
from utils.export_util import ExportUtil
from utils.common_util import CamelCaseUtil
from utils.page_util import PageUtil


class TechnicalManualPriceService(BaseService[TechnicalManualPrice]):
    """
    技术手册价格管理模块服务层
    """

    def __init__(self, db: AsyncSession):
        """
        初始化技术手册价格服务

        :param db: 数据库会话
        """
        super().__init__(TechnicalManualPrice, db)
        self.db = db

    async def get_technical_manual_price_list(self, query_object: TechnicalManualPriceQueryModel):
        """
        获取技术手册价格列表

        :param query_object: 查询参数对象
        :return: 技术手册价格列表
        """
        # 构建查询条件
        conditions = [TechnicalManualPrice.del_flag == '0']

        if query_object.method:
            conditions.append(TechnicalManualPrice.method.like(f'%{query_object.method}%'))
        if query_object.category:
            conditions.append(TechnicalManualPrice.category.like(f'%{query_object.category}%'))
        if query_object.classification:
            conditions.append(TechnicalManualPrice.classification.like(f'%{query_object.classification}%'))
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManualPrice.method.like(f'%{query_object.keyword}%'),
                TechnicalManualPrice.category.like(f'%{query_object.keyword}%'),
                TechnicalManualPrice.classification.like(f'%{query_object.keyword}%'),
                TechnicalManualPrice.remark.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                TechnicalManualPrice.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManualPrice.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(TechnicalManualPrice).where(and_(*conditions)).order_by(
            TechnicalManualPrice.method, TechnicalManualPrice.category
        )
        result = await self.db.execute(stmt)
        technical_manual_price_list = result.scalars().all()

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manual_price_list)

    async def get_technical_manual_price_page(self, query_object: TechnicalManualPricePageQueryModel):
        """
        获取技术手册价格分页列表

        :param query_object: 查询参数对象
        :return: 技术手册价格分页列表
        """
        # 构建查询条件
        conditions = [TechnicalManualPrice.del_flag == '0']

        if query_object.method:
            conditions.append(TechnicalManualPrice.method.like(f'%{query_object.method}%'))
        if query_object.category:
            conditions.append(TechnicalManualPrice.category.like(f'%{query_object.category}%'))
        if query_object.classification:
            conditions.append(TechnicalManualPrice.classification.like(f'%{query_object.classification}%'))
        if query_object.keyword:
            keyword_condition = or_(
                TechnicalManualPrice.method.like(f'%{query_object.keyword}%'),
                TechnicalManualPrice.category.like(f'%{query_object.category}%'),
                TechnicalManualPrice.classification.like(f'%{query_object.classification}%'),
                TechnicalManualPrice.remark.like(f'%{query_object.keyword}%')
            )
            conditions.append(keyword_condition)
        if query_object.begin_time and query_object.end_time:
            from datetime import time
            time_condition = and_(
                TechnicalManualPrice.create_time >= datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(00, 00, 00)),
                TechnicalManualPrice.create_time <= datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59))
            )
            conditions.append(time_condition)

        # 执行查询
        stmt = select(TechnicalManualPrice).where(and_(*conditions)).order_by(
            TechnicalManualPrice.method, TechnicalManualPrice.category
        )
        page_result = await PageUtil.paginate(self.db, stmt, query_object.page_num, query_object.page_size, True)

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(page_result)

    async def get_technical_manual_price_detail(self, id: int):
        """
        获取技术手册价格详情

        :param id: 技术手册价格ID
        :return: 技术手册价格详情
        """
        stmt = select(TechnicalManualPrice).where(TechnicalManualPrice.id == id)
        result = await self.db.execute(stmt)
        technical_manual_price = result.scalars().first()
        if not technical_manual_price:
            raise ServiceException(message=f'技术手册价格ID：{id}不存在')

        # 将下划线命名转换为驼峰命名
        return CamelCaseUtil.transform_result(technical_manual_price)

    async def check_technical_manual_price_unique(self, method: str, category: str, id: int = None):
        """
        检查技术手册价格是否唯一

        :param method: 检测方法
        :param category: 检测类别
        :param id: 技术手册价格ID
        :return: 是否唯一
        """
        conditions = [
            TechnicalManualPrice.method == method,
            TechnicalManualPrice.category == category,
            TechnicalManualPrice.del_flag == '0'
        ]

        if id:
            conditions.append(TechnicalManualPrice.id != id)

        stmt = select(TechnicalManualPrice).where(and_(*conditions))
        result = await self.db.execute(stmt)
        return result.first() is None

    async def get_method_category_options(self):
        """
        获取检测方法和类别选项

        :return: 检测方法和类别选项列表
        """
        # 从技术手册中获取不重复的检测方法
        stmt = select(distinct(TechnicalManual.method)).where(
            TechnicalManual.del_flag == '0',
            TechnicalManual.status == '0'
        ).order_by(TechnicalManual.method)
        result = await self.db.execute(stmt)
        methods = result.scalars().all()

        # 为每个检测方法获取对应的检测类别和分类
        method_category_options = []
        for method in methods:
            # 获取该检测方法对应的检测类别和分类
            category_stmt = select(
                distinct(TechnicalManual.category),
                TechnicalManual.classification
            ).where(
                TechnicalManual.method == method,
                TechnicalManual.del_flag == '0',
                TechnicalManual.status == '0'
            ).order_by(TechnicalManual.category)

            category_result = await self.db.execute(category_stmt)
            categories_data = category_result.all()

            # 构建类别列表
            categories = []
            for category_row in categories_data:
                category = category_row[0]
                classification = category_row[1] or ''
                # 显示格式：[分类]检测类别
                display_text = f'[{classification}]{category}' if classification else category
                categories.append({
                    'category': category,
                    'classification': classification,
                    'displayText': display_text
                })

            if categories:  # 只有当该方法有对应的类别时才添加
                method_category_options.append({
                    'method': method,
                    'categories': categories
                })

        return method_category_options

    async def add_technical_manual_price(self, _: Request, technical_manual_price_model: AddTechnicalManualPriceModel, current_user: CurrentUserModel):
        """
        新增技术手册价格

        :param request: 请求对象
        :param technical_manual_price_model: 新增技术手册价格对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 新增结果
        """
        try:
            # 校验技术手册价格是否唯一
            if not await self.check_technical_manual_price_unique(
                technical_manual_price_model.method,
                technical_manual_price_model.category
            ):
                raise ServiceException(message=f'新增技术手册价格失败，该检测方法和类别组合已存在')

            # 创建技术手册价格对象
            technical_manual_price = TechnicalManualPrice(
                method=technical_manual_price_model.method,
                category=technical_manual_price_model.category,
                classification=technical_manual_price_model.classification,
                # 检测价格相关字段
                first_item_price=technical_manual_price_model.first_item_price,
                additional_item_price=technical_manual_price_model.additional_item_price,
                testing_fee_limit=technical_manual_price_model.testing_fee_limit,
                # 采集价格相关字段
                wastewater_sampling_price=technical_manual_price_model.wastewater_sampling_price,
                organized_waste_gas_sampling_price=technical_manual_price_model.organized_waste_gas_sampling_price,
                ambient_air_daily_sampling_price=technical_manual_price_model.ambient_air_daily_sampling_price,
                unorganized_waste_gas_sampling_price=technical_manual_price_model.unorganized_waste_gas_sampling_price,
                ambient_air_hourly_sampling_price=technical_manual_price_model.ambient_air_hourly_sampling_price,
                surface_water_sampling_price=technical_manual_price_model.surface_water_sampling_price,
                groundwater_sampling_price=technical_manual_price_model.groundwater_sampling_price,
                soil_sampling_price=technical_manual_price_model.soil_sampling_price,
                daytime_noise_sampling_price=technical_manual_price_model.daytime_noise_sampling_price,
                nighttime_noise_sampling_price=technical_manual_price_model.nighttime_noise_sampling_price,
                daily_noise_sampling_price=technical_manual_price_model.daily_noise_sampling_price,
                seawater_sampling_price=technical_manual_price_model.seawater_sampling_price,
                ecology_sampling_price=technical_manual_price_model.ecology_sampling_price,
                radiation_sampling_price=technical_manual_price_model.radiation_sampling_price,
                vibration_sampling_price=technical_manual_price_model.vibration_sampling_price,
                energy_sampling_price=technical_manual_price_model.energy_sampling_price,
                solid_waste_sampling_price=technical_manual_price_model.solid_waste_sampling_price,
                sediment_sampling_price=technical_manual_price_model.sediment_sampling_price,
                pretreatment_price=technical_manual_price_model.pretreatment_price,
                # 其他价格相关字段
                special_consumables_price=technical_manual_price_model.special_consumables_price,
                # 系统字段
                status=technical_manual_price_model.status,
                create_by=current_user.user.user_name if current_user and current_user.user else '',
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else '',
                update_time=datetime.now(),
                remark=technical_manual_price_model.remark
            )

            # 新增技术手册价格
            self.db.add(technical_manual_price)
            await self.db.flush()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='新增成功',
                result={'id': technical_manual_price.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("添加技术手册价格失败", e)
            raise ServiceException(message=f'新增技术手册价格失败: {str(e)}')

    async def edit_technical_manual_price(self, _: Request, technical_manual_price_model: EditTechnicalManualPriceModel, current_user: CurrentUserModel):
        """
        编辑技术手册价格

        :param request: 请求对象
        :param technical_manual_price_model: 编辑技术手册价格对象
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 编辑结果
        """
        try:
            # 获取技术手册价格对象
            stmt = select(TechnicalManualPrice).where(TechnicalManualPrice.id == technical_manual_price_model.id)
            result = await self.db.execute(stmt)
            technical_manual_price = result.scalars().first()
            if not technical_manual_price:
                raise ServiceException(message=f'技术手册价格ID：{technical_manual_price_model.id}不存在')

            # 校验技术手册价格是否唯一
            if not await self.check_technical_manual_price_unique(
                technical_manual_price_model.method,
                technical_manual_price_model.category,
                technical_manual_price_model.id
            ):
                raise ServiceException(message=f'修改技术手册价格失败，该检测方法和类别组合已存在')

            # 更新技术手册价格对象
            technical_manual_price.method = technical_manual_price_model.method
            technical_manual_price.category = technical_manual_price_model.category
            technical_manual_price.classification = technical_manual_price_model.classification
            # 检测价格相关字段
            technical_manual_price.first_item_price = technical_manual_price_model.first_item_price
            technical_manual_price.additional_item_price = technical_manual_price_model.additional_item_price
            technical_manual_price.testing_fee_limit = technical_manual_price_model.testing_fee_limit
            # 采集价格相关字段
            technical_manual_price.wastewater_sampling_price = technical_manual_price_model.wastewater_sampling_price
            technical_manual_price.organized_waste_gas_sampling_price = technical_manual_price_model.organized_waste_gas_sampling_price
            technical_manual_price.ambient_air_daily_sampling_price = technical_manual_price_model.ambient_air_daily_sampling_price
            technical_manual_price.unorganized_waste_gas_sampling_price = technical_manual_price_model.unorganized_waste_gas_sampling_price
            technical_manual_price.ambient_air_hourly_sampling_price = technical_manual_price_model.ambient_air_hourly_sampling_price
            technical_manual_price.surface_water_sampling_price = technical_manual_price_model.surface_water_sampling_price
            technical_manual_price.groundwater_sampling_price = technical_manual_price_model.groundwater_sampling_price
            technical_manual_price.soil_sampling_price = technical_manual_price_model.soil_sampling_price
            technical_manual_price.daytime_noise_sampling_price = technical_manual_price_model.daytime_noise_sampling_price
            technical_manual_price.nighttime_noise_sampling_price = technical_manual_price_model.nighttime_noise_sampling_price
            technical_manual_price.daily_noise_sampling_price = technical_manual_price_model.daily_noise_sampling_price
            technical_manual_price.seawater_sampling_price = technical_manual_price_model.seawater_sampling_price
            technical_manual_price.ecology_sampling_price = technical_manual_price_model.ecology_sampling_price
            technical_manual_price.radiation_sampling_price = technical_manual_price_model.radiation_sampling_price
            technical_manual_price.vibration_sampling_price = technical_manual_price_model.vibration_sampling_price
            technical_manual_price.energy_sampling_price = technical_manual_price_model.energy_sampling_price
            technical_manual_price.solid_waste_sampling_price = technical_manual_price_model.solid_waste_sampling_price
            technical_manual_price.sediment_sampling_price = technical_manual_price_model.sediment_sampling_price
            technical_manual_price.pretreatment_price = technical_manual_price_model.pretreatment_price
            # 其他价格相关字段
            technical_manual_price.special_consumables_price = technical_manual_price_model.special_consumables_price
            # 系统字段
            technical_manual_price.status = technical_manual_price_model.status
            technical_manual_price.update_by = current_user.user.user_name if current_user and current_user.user else ''
            technical_manual_price.update_time = datetime.now()
            technical_manual_price.remark = technical_manual_price_model.remark

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='修改成功',
                result={'id': technical_manual_price.id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("修改技术手册价格失败", e)
            raise ServiceException(message=f'修改技术手册价格失败: {str(e)}')

    async def delete_technical_manual_price(self, id: int, current_user: CurrentUserModel):
        """
        删除技术手册价格

        :param id: 技术手册价格ID
        :param current_user: 当前用户对象(CurrentUserModel)
        :return: 删除结果
        """
        try:
            # 获取技术手册价格对象
            stmt = select(TechnicalManualPrice).where(TechnicalManualPrice.id == id)
            result = await self.db.execute(stmt)
            technical_manual_price = result.scalars().first()
            if not technical_manual_price:
                raise ServiceException(message=f'技术手册价格ID：{id}不存在')

            # 软删除技术手册价格
            technical_manual_price.del_flag = '2'
            technical_manual_price.update_by = current_user.user.user_name if current_user and current_user.user else ''
            technical_manual_price.update_time = datetime.now()

            # 提交事务
            await self.db.commit()

            # 返回结果
            return CrudResponseModel(
                is_success=True,
                message='删除成功',
                result={'id': id}
            )
        except Exception as e:
            # 回滚事务
            await self.db.rollback()
            # 记录错误并重新抛出
            self.log_error("删除技术手册价格失败", e)
            raise ServiceException(message=f'删除技术手册价格失败: {str(e)}')

    async def export_technical_manual_price(self, query_object: TechnicalManualPriceQueryModel, export_type: str) -> Response:
        """
        导出技术手册价格

        :param query_object: 查询参数对象
        :param export_type: 导出类型（excel, pdf, word）
        :return: 导出文件响应
        """
        # 获取技术手册价格列表
        technical_manual_prices = await self.get_technical_manual_price_list(query_object)

        # 转换为字典列表
        data = []
        for price in technical_manual_prices:
            # 将SQLAlchemy模型转换为字典
            price_dict = {
                'id': price.id,
                'method': price.method,
                'category': price.category,
                'classification': price.classification or '',
                'first_item_price': str(price.first_item_price) if price.first_item_price else '',
                'additional_item_price': str(price.additional_item_price) if price.additional_item_price else '',
                'testing_fee_limit': str(price.testing_fee_limit) if price.testing_fee_limit else '',
                'status': '启用' if price.status == '0' else '停用',
                'create_time': price.create_time.strftime('%Y-%m-%d %H:%M:%S') if price.create_time else '',
                'create_by': price.create_by,
                'remark': price.remark or ''
            }
            data.append(price_dict)

        # 定义表头映射
        headers = {
            'id': 'ID',
            'method': '检测方法',
            'category': '检测类别',
            'classification': '分类',
            'first_item_price': '检测首项单价',
            'additional_item_price': '检测增项单价',
            'testing_fee_limit': '检测费上限',
            'status': '状态',
            'create_time': '创建时间',
            'create_by': '创建人',
            'remark': '备注'
        }

        # 导出文件
        return ExportUtil.export_data(data, headers, '技术手册价格', export_type)
