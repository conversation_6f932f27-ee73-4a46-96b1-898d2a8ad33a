<template>
  <div class="app-container">
    <a-card :bordered="false">
      <!-- 查询条件 -->
      <a-form layout="inline" :model="queryParams" @finish="handleQuery">
        <a-form-item label="检测类别" name="category">
          <a-select
            v-model:value="queryParams.category"
            placeholder="请选择检测类别"
            allowClear
            style="min-width: 100px;"
            @change="handleCategoryChange"
          >
            <a-select-option v-for="item in categoryOptions" :key="item.category" :value="item.category">
              {{ item.category }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检测参数" name="parameter">
          <a-select
            v-model:value="queryParams.parameter"
            placeholder="请选择检测参数"
            allowClear
            style="min-width: 100px;"
            @change="handleParameterChange"
          >
            <a-select-option v-for="item in parameterOptions" :key="item.parameter" :value="item.parameter">
              {{ item.parameter }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="检测方法" name="method">
          <a-select
            v-model:value="queryParams.method"
            placeholder="请选择检测方法"
            allowClear
            style="min-width: 100px;"
          >
            <a-select-option v-for="item in methodOptions" :key="item.method" :value="item.method">
              {{ item.method }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="关键字" name="keyword">
          <a-input v-model:value="queryParams.keyword" placeholder="请输入关键字" allowClear />
        </a-form-item>
        <a-form-item label="创建时间" name="dateRange">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            :placeholder="['开始日期', '结束日期']"
            @change="handleDateRangeChange"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button @click="resetQuery">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 操作按钮 -->
      <div style="margin-bottom: 16px; margin-top: 16px;">
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon><PlusOutlined /></template>
            新增
          </a-button>
          <!-- 导出按钮 -->
          <export-button export-url="/api/basedata/technical-manual/export" :query-params="queryParams" />
        </a-space>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="list"
        :loading="loading"
        :pagination="{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: pagination.showSizeChanger,
          showTotal: pagination.showTotal,
          onChange: (page, pageSize) => handleTableChange({current: page, pageSize: pageSize})
        }"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === '0' ? 'green' : 'red'">
              {{ record.status === '0' ? '启用' : '停用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除该技术手册吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 新增/编辑弹窗 -->
      <a-modal
        :visible="open"
        :title="title"
        :confirm-loading="submitLoading"
        @ok="handleSubmit"
        @cancel="cancel"
      >
        <a-form
          ref="formRef"
          :model="form"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="检测类别" name="category">
            <a-select
              v-model:value="form.category"
              placeholder="请选择检测类别"
              style="width: 100%"
              @change="handleFormCategoryChange"
            >
              <a-select-option v-for="item in categoryOptions" :key="item.category" :value="item.category">
                {{ item.category }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="检测参数" name="parameter">
            <a-select
              v-model:value="form.parameter"
              placeholder="请选择检测参数"
              style="width: 100%"
              @change="handleFormParameterChange"
            >
              <a-select-option v-for="item in parameterOptions" :key="item.parameter" :value="item.parameter">
                {{ item.parameter }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="检测方法" name="method">
            <a-select
              v-model:value="form.method"
              placeholder="请选择检测方法"
              style="width: 100%"
            >
              <a-select-option v-for="item in methodOptions" :key="item.method" :value="item.method">
                {{ item.method }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="描述" name="description">
            <a-textarea v-model:value="form.description" placeholder="请输入描述" :rows="3" />
          </a-form-item>
          <a-form-item label="标准" name="standard">
            <a-textarea v-model:value="form.standard" placeholder="请输入标准" :rows="3" />
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-radio-group v-model:value="form.status">
              <a-radio value="0">启用</a-radio>
              <a-radio value="1">停用</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" :rows="3" />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import {
  getTechnicalManualPage,
  getTechnicalManualDetail,
  addTechnicalManual,
  editTechnicalManual,
  deleteTechnicalManual,
  getCategories,
  getParameters,
  getMethods
} from '@/api/basedata/technical-manual';
import ExportButton from '@/components/ExportButton.vue';

export default defineComponent({
  name: 'TechnicalManual',
  components: {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    ExportButton
  },
  setup() {
    // 列表数据
    const list = ref([]);
    const loading = ref(false);
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`
    });

    // 查询参数
    const queryParams = reactive({
      pageNum: 1,
      pageSize: 10,
      category: undefined,
      parameter: undefined,
      method: undefined,
      keyword: undefined,
      beginTime: undefined,
      endTime: undefined
    });
    const dateRange = ref([]);

    // 下拉选项
    const categoryOptions = ref([]);
    const parameterOptions = ref([]);
    const methodOptions = ref([]);

    // 表单数据
    const formRef = ref(null);
    const open = ref(false);
    const title = ref('');
    const submitLoading = ref(false);
    const form = reactive({
      id: undefined,
      category: undefined,
      parameter: undefined,
      method: undefined,
      description: undefined,
      standard: undefined,
      status: '0',
      remark: undefined
    });
    const rules = {
      category: [{ required: true, message: '请选择检测类别', trigger: 'change' }],
      parameter: [{ required: true, message: '请选择检测参数', trigger: 'change' }],
      method: [{ required: true, message: '请选择检测方法', trigger: 'change' }],
      description: [{ required: true, message: '请输入描述', trigger: 'blur' }],
      standard: [{ required: true, message: '请输入标准', trigger: 'blur' }],
      status: [{ required: true, message: '请选择状态', trigger: 'change' }]
    };

    // 表格列定义
    const columns = [
      { title: '检测类别', dataIndex: 'category', key: 'category', width: 150 },
      { title: '检测参数', dataIndex: 'parameter', key: 'parameter', width: 150 },
      { title: '检测方法', dataIndex: 'method', key: 'method', width: 150 },
      { title: '描述', dataIndex: 'description', key: 'description', ellipsis: true },
      { title: '标准', dataIndex: 'standard', key: 'standard', ellipsis: true },
      { title: '状态', dataIndex: 'status', key: 'status', width: 100 },
      { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 180 },
      { title: '操作', key: 'action', width: 150 }
    ];

    // 获取列表数据
    const getList = async () => {
      loading.value = true;
      try {
        const res = await getTechnicalManualPage(queryParams);
        // 检查返回数据格式
        if (res.data && Array.isArray(res.data.items)) {
          // 标准格式：{items: [...], total: 100}
          list.value = res.data.items;
          pagination.total = res.data.total;
        } else if (res.data && Array.isArray(res.data.records)) {
          // 另一种可能的格式：{records: [...], total: 100}
          list.value = res.data.records;
          pagination.total = res.data.total;
        } else if (Array.isArray(res.data)) {
          // 直接返回数组的情况
          list.value = res.data;
          pagination.total = res.data.length;
        } else {
          list.value = [];
          pagination.total = 0;
          console.error('未知的返回数据格式', res.data);
        }

        // 更新分页信息
        pagination.current = queryParams.pageNum;
        pagination.pageSize = queryParams.pageSize;
      } catch (error) {
        console.error('获取技术手册列表失败', error);
        message.error('获取技术手册列表失败');
        list.value = [];
        pagination.total = 0;
      } finally {
        loading.value = false;
      }
    };

    // 获取检测类别选项
    const getCategoryOptions = async () => {
      try {
        const res = await getCategories();
        categoryOptions.value = res.data || [];
      } catch (error) {
        console.error('获取检测类别失败', error);
        message.error('获取检测类别失败');
      }
    };

    // 获取检测参数选项
    const getParameterOptions = async (category) => {
      try {
        const res = await getParameters(category);
        parameterOptions.value = res.data || [];
      } catch (error) {
        console.error('获取检测参数失败', error);
        message.error('获取检测参数失败');
      }
    };

    // 获取检测方法选项
    const getMethodOptions = async (category, parameter) => {
      try {
        const res = await getMethods(category, parameter);
        methodOptions.value = res.data || [];
      } catch (error) {
        console.error('获取检测方法失败', error);
        message.error('获取检测方法失败');
      }
    };

    // 查询条件变更处理
    const handleCategoryChange = (value) => {
      queryParams.category = value;
      queryParams.parameter = undefined;
      queryParams.method = undefined;
      getParameterOptions(value);
      methodOptions.value = [];
    };

    const handleParameterChange = (value) => {
      queryParams.parameter = value;
      queryParams.method = undefined;
      getMethodOptions(queryParams.category, value);
    };

    const handleDateRangeChange = (_, dateStrings) => {
      queryParams.beginTime = dateStrings[0] || undefined;
      queryParams.endTime = dateStrings[1] || undefined;
    };

    // 表单选择变更处理
    const handleFormCategoryChange = (value) => {
      form.category = value;
      form.parameter = undefined;
      form.method = undefined;
      getParameterOptions(value);
      methodOptions.value = [];
    };

    const handleFormParameterChange = (value) => {
      form.parameter = value;
      form.method = undefined;
      getMethodOptions(form.category, value);
    };

    // 查询操作
    const handleQuery = () => {
      queryParams.pageNum = 1;
      getList();
    };

    // 重置查询
    const resetQuery = () => {
      dateRange.value = [];
      Object.assign(queryParams, {
        pageNum: 1,
        pageSize: 10,
        category: undefined,
        parameter: undefined,
        method: undefined,
        keyword: undefined,
        beginTime: undefined,
        endTime: undefined
      });
      getList();
    };

    // 表格变更处理
    const handleTableChange = (pag) => {
      // 更新分页参数
      queryParams.pageNum = pag.current;
      queryParams.pageSize = pag.pageSize;
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      // 重新获取数据
      getList();
    };

    // 新增操作
    const handleAdd = () => {
      open.value = true;
      title.value = '新增技术手册';
      resetForm();
    };

    // 编辑操作
    const handleEdit = async (record) => {
      open.value = true;
      title.value = '编辑技术手册';
      resetForm();

      try {
        const res = await getTechnicalManualDetail(record.id);
        Object.assign(form, res.data);

        // 加载相关选项
        await getParameterOptions(form.category);
        await getMethodOptions(form.category, form.parameter);
      } catch (error) {
        console.error('获取技术手册详情失败', error);
        message.error('获取技术手册详情失败');
      }
    };

    // 删除操作
    const handleDelete = async (record) => {
      try {
        await deleteTechnicalManual(record.id);
        message.success('删除成功');
        getList();
      } catch (error) {
        console.error('删除技术手册失败', error);
        message.error('删除技术手册失败');
      }
    };

    // 表单提交
    const handleSubmit = async () => {
      try {
        await formRef.value.validate();
        submitLoading.value = true;

        if (form.id) {
          // 编辑
          await editTechnicalManual(form);
          message.success('修改成功');
        } else {
          // 新增
          await addTechnicalManual(form);
          message.success('新增成功');
        }

        open.value = false;
        getList();
      } catch (error) {
        console.error('提交失败', error);
        message.error('提交失败');
      } finally {
        submitLoading.value = false;
      }
    };

    // 取消操作
    const cancel = () => {
      open.value = false;
      resetForm();
    };

    // 重置表单
    const resetForm = () => {
      if (formRef.value) {
        formRef.value.resetFields();
      }
      Object.assign(form, {
        id: undefined,
        category: undefined,
        parameter: undefined,
        method: undefined,
        description: undefined,
        standard: undefined,
        status: '0',
        remark: undefined
      });
    };

    // 初始化
    onMounted(() => {
      getList();
      getCategoryOptions();
    });

    return {
      // 数据
      list,
      loading,
      pagination,
      queryParams,
      dateRange,
      categoryOptions,
      parameterOptions,
      methodOptions,
      columns,

      // 表单
      formRef,
      open,
      title,
      submitLoading,
      form,
      rules,

      // 方法
      getList,
      handleCategoryChange,
      handleParameterChange,
      handleDateRangeChange,
      handleFormCategoryChange,
      handleFormParameterChange,
      handleQuery,
      resetQuery,
      handleTableChange,
      handleAdd,
      handleEdit,
      handleDelete,
      handleSubmit,
      cancel
    };
  }
});
</script>
