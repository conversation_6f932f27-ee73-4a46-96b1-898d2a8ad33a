<template>
  <div class="app-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>采样点位安排组件演示</span>
        </div>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <h3>功能说明</h3>
            <ul>
              <li>点位名称：手动录入，必填项</li>
              <li>点位数：每项参数对应点位数量，手动录入</li>
              <li>周期类型：支持日、周、月、季、年五种，单选，默认为日</li>
              <li>周期数：周期内的总周期次数，手动录入，必填项</li>
              <li>检测频次：每周期的采样频次，手动录入，必填项</li>
              <li>样品数：每次采样涉及的样品数量，手动录入，必填项</li>
              <li>自动计算样品总数并显示预览信息</li>
            </ul>
            
            <div class="demo-controls">
              <el-form :model="demoForm" label-width="120px">
                <el-form-item label="预设数据">
                  <el-select v-model="selectedPreset" placeholder="选择预设数据" @change="handlePresetChange">
                    <el-option label="默认设置" value="default" />
                    <el-option label="水质监测" value="water" />
                    <el-option label="大气监测" value="air" />
                    <el-option label="土壤监测" value="soil" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="openSamplingPointArrangement">
                    打开采样点位安排
                  </el-button>
                  <el-button @click="clearResult">清空结果</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-col>
          
          <el-col :span="12">
            <h3>安排结果</h3>
            <div v-if="arrangementResult && Object.keys(arrangementResult).length > 0" class="result-display">
              <el-descriptions title="采样点位安排详情" :column="2" border>
                <el-descriptions-item label="点位名称">{{ arrangementResult.pointName }}</el-descriptions-item>
                <el-descriptions-item label="点位数">{{ arrangementResult.pointCount }} 个</el-descriptions-item>
                <el-descriptions-item label="周期类型">{{ arrangementResult.cycleType }}</el-descriptions-item>
                <el-descriptions-item label="周期数">{{ arrangementResult.cycleCount }} 个</el-descriptions-item>
                <el-descriptions-item label="检测频次">{{ arrangementResult.frequency }} 次/{{ arrangementResult.cycleType }}</el-descriptions-item>
                <el-descriptions-item label="样品数">{{ arrangementResult.sampleCount }} 个/次</el-descriptions-item>
                <el-descriptions-item label="样品总数" :span="2">
                  <el-tag type="success" size="large">{{ arrangementResult.totalSamples }} 个样品</el-tag>
                </el-descriptions-item>
              </el-descriptions>
              
              <div class="calculation-formula">
                <h4>计算公式：</h4>
                <p>
                  样品总数 = 点位数 × 周期数 × 检测频次 × 样品数<br>
                  {{ arrangementResult.totalSamples }} = {{ arrangementResult.pointCount }} × {{ arrangementResult.cycleCount }} × {{ arrangementResult.frequency }} × {{ arrangementResult.sampleCount }}
                </p>
              </div>
            </div>
            <div v-else class="no-result">
              <el-empty description="暂无安排结果" :image-size="100" />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 采样点位安排组件 -->
    <sampling-point-arrangement
      :visible="samplingPointOpen"
      @update:visible="samplingPointOpen = $event"
      :initial-data="initialData"
      @confirm="handleSamplingPointConfirm"
      title="安排采样点位"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import SamplingPointArrangement from '@/components/SamplingPointArrangement/index.vue'

// 演示表单
const demoForm = reactive({})

// 选中的预设
const selectedPreset = ref('default')

// 是否显示采样点位安排弹出层
const samplingPointOpen = ref(false)

// 初始数据
const initialData = ref({})

// 安排结果
const arrangementResult = ref({})

// 预设数据
const presetData = {
  default: {
    pointName: '',
    pointCount: 1,
    cycleType: '日',
    cycleCount: 1,
    frequency: 1,
    sampleCount: 1
  },
  water: {
    pointName: '河流监测点',
    pointCount: 3,
    cycleType: '月',
    cycleCount: 12,
    frequency: 2,
    sampleCount: 3
  },
  air: {
    pointName: '大气监测站',
    pointCount: 5,
    cycleType: '日',
    cycleCount: 30,
    frequency: 4,
    sampleCount: 1
  },
  soil: {
    pointName: '土壤采样点',
    pointCount: 10,
    cycleType: '季',
    cycleCount: 4,
    frequency: 1,
    sampleCount: 5
  }
}

/** 处理预设数据变更 */
function handlePresetChange() {
  initialData.value = { ...presetData[selectedPreset.value] }
}

/** 打开采样点位安排 */
function openSamplingPointArrangement() {
  samplingPointOpen.value = true
}

/** 处理采样点位安排确认 */
function handleSamplingPointConfirm(result) {
  arrangementResult.value = { ...result }
  
  // 显示安排结果
  ElMessage.success(`采样点位安排完成：${result.pointName}`)
  console.log('采样点位安排结果:', result)
}

/** 清空结果 */
function clearResult() {
  arrangementResult.value = {}
  ElMessage.info('已清空安排结果')
}

// 初始化
handlePresetChange()
</script>

<style scoped>
.demo-content {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.demo-controls {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.result-display {
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #e1f5fe;
  border-radius: 4px;
}

.calculation-formula {
  margin-top: 20px;
  padding: 15px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
}

.calculation-formula h4 {
  margin: 0 0 10px 0;
  color: #d46b08;
}

.calculation-formula p {
  margin: 0;
  color: #8c4a00;
  line-height: 1.6;
}

.no-result {
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

h3 {
  color: #303133;
  margin-bottom: 15px;
}

ul {
  color: #606266;
  line-height: 1.6;
}

li {
  margin-bottom: 8px;
}
</style>
