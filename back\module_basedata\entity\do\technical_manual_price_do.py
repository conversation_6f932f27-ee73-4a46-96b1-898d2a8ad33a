from datetime import datetime
from sqlalchemy import Column, DateTime, Integer, String, Text, BigInteger, DECIMAL, Index
from config.database import Base


class TechnicalManualPrice(Base):
    """
    技术手册价格表
    """

    __tablename__ = 'technical_manual_price'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 唯一标识字段
    method = Column(String(100), nullable=False, comment='检测方法')
    category = Column(String(50), nullable=False, comment='检测类别')
    classification = Column(String(50), nullable=True, comment='分类')
    
    # 检测价格相关字段
    first_item_price = Column(DECIMAL(10, 2), nullable=True, comment='检测首项单价')
    additional_item_price = Column(DECIMAL(10, 2), nullable=True, comment='检测增项单价')
    testing_fee_limit = Column(DECIMAL(10, 2), nullable=True, comment='检测费上限')
    
    # 采集价格相关字段
    wastewater_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='废水采集单价')
    organized_waste_gas_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='有组织废气采集单价')
    ambient_air_daily_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='环境空气日均值采集单价')
    unorganized_waste_gas_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='无组织废气采集单价')
    ambient_air_hourly_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='环境空气小时值采集单价')
    surface_water_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='地表水采集单价')
    groundwater_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='地下水采集单价')
    soil_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='土壤采集单价')
    daytime_noise_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='昼间噪声采集单价')
    nighttime_noise_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='夜间噪声采集单价')
    daily_noise_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='24小时噪声采集单价')
    seawater_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='海水采集单价')
    ecology_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='生态采集单价')
    radiation_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='辐射采集单价')
    vibration_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='震动采集单价')
    energy_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='能源采集单价')
    solid_waste_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='固废采集单价')
    sediment_sampling_price = Column(DECIMAL(10, 2), nullable=True, comment='底泥采集单价')
    pretreatment_price = Column(DECIMAL(10, 2), nullable=True, comment='前处理单价')
    
    # 其他价格相关字段
    special_consumables_price = Column(DECIMAL(10, 2), nullable=True, comment='分析特殊耗材单价')
    
    # 系统字段
    status = Column(String(1), default='0', comment='状态（0正常 1停用）')
    del_flag = Column(String(1), default='0', comment='删除标志（0存在 2删除）')
    create_by = Column(String(64), default='', comment='创建者')
    create_time = Column(DateTime, default=datetime.now(), comment='创建时间')
    update_by = Column(String(64), default='', comment='更新者')
    update_time = Column(DateTime, default=datetime.now(), comment='更新时间')
    remark = Column(String(500), default='', comment='备注')

    # 创建唯一索引
    __table_args__ = (
        Index('idx_technical_manual_price_unique', 'method', 'category', unique=True),
    )
