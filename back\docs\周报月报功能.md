# 周报月报功能
1. 分为周报和月报两个tab
2. 周报和月报的填报人都是当前登录用户
3. 周报月报的内容分为报告时间（选中周/月的第一天）本周/本月总结，下周/下月计划，本周/本月存在的问题，以及需要的支持，这些内容通过不同字段存储。周报需要有下周工作是否饱和字段，可选饱和和不饱和。
4. 周报列表页面展示报告人，报告日期 ，创建时间，本周/本月总结，下周/下月计划，是否饱和，操作列（编辑，删除，查看）。
5. 列表页可以根据报告人、报告日期、是否饱和搜索。
6. 权限控制，当前登录用户可以查看自己和自己下属员工的周报月报。下属员工为该用户负责的部门的所有员工。
7. 该功能单独做一个模块，名称为module_report

# 实现步骤
1. 生成数据库表
2. 生成后端代码和前端页面代码
3. 生成单元测试并验证功能完整性

# 注意点
1. 整体样式和代码风格要和现有的保持一致。可以参考客户管理的实现(model_customer)
